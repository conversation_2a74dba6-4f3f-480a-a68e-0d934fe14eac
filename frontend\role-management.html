<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色管理 - 金融软件项目管理系统</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/role-management.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                <h1>金融软件项目管理系统</h1>
            </div>
            <nav class="nav">
                <a href="index.html" class="btn btn-secondary">
                    <i class="fas fa-home"></i> 返回首页
                </a>
                <div class="user-info">
                    <span>欢迎，管理员</span>
                    <i class="fas fa-user-circle"></i>
                </div>
            </nav>
        </header>

        <main class="main">
            <div class="page-header">
                <h2><i class="fas fa-users-cog"></i> 角色管理</h2>
                <p>管理系统角色，配置角色权限和状态</p>
            </div>

            <div class="content-layout">
                <!-- 左侧操作面板 -->
                <div class="sidebar">
                    <div class="operation-panel">
                        <h3><i class="fas fa-tools"></i> 操作菜单</h3>
                        <div class="operation-buttons">
                            <button class="btn btn-primary" onclick="showAddModal()">
                                <i class="fas fa-plus"></i> 新增角色
                            </button>
                            <button class="btn btn-success" onclick="editSelected()" id="editBtn" disabled>
                                <i class="fas fa-edit"></i> 编辑角色
                            </button>
                            <button class="btn btn-danger" onclick="deleteSelected()" id="deleteBtn" disabled>
                                <i class="fas fa-trash"></i> 删除角色
                            </button>
                        </div>
                        
                        <div class="search-section">
                            <h4><i class="fas fa-search"></i> 搜索筛选</h4>
                            <div class="form-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="按角色名称搜索...">
                            </div>
                            <div class="form-group">
                                <select class="form-control form-select" id="statusFilter">
                                    <option value="">全部状态</option>
                                    <option value="enabled">启用</option>
                                    <option value="disabled">禁用</option>
                                </select>
                            </div>
                        </div>

                        <div class="stats-section">
                            <h4><i class="fas fa-chart-pie"></i> 统计信息</h4>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-number" id="totalRoles">0</div>
                                    <div class="stat-label">总角色数</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number" id="enabledRoles">0</div>
                                    <div class="stat-label">启用角色</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number" id="disabledRoles">0</div>
                                    <div class="stat-label">禁用角色</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧角色列表 -->
                <div class="main-content">
                    <div class="table-container">
                        <div class="table-header">
                            <h3><i class="fas fa-list"></i> 角色列表</h3>
                            <div class="table-actions">
                                <button class="btn btn-secondary" onclick="refreshTable()">
                                    <i class="fas fa-sync-alt"></i> 刷新
                                </button>
                                <button class="btn btn-secondary" onclick="exportData()">
                                    <i class="fas fa-download"></i> 导出
                                </button>
                            </div>
                        </div>
                        
                        <table class="table" id="rolesTable">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>角色ID</th>
                                    <th>角色名称</th>
                                    <th>角色描述</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="rolesTableBody">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>

                        <div class="table-pagination">
                            <div class="pagination-info">
                                <span id="pageInfo">显示 1-10 条，共 25 条</span>
                            </div>
                            <div class="pagination-controls">
                                <button class="btn btn-secondary" id="prevPage" onclick="prevPage()">
                                    <i class="fas fa-chevron-left"></i> 上一页
                                </button>
                                <div class="page-numbers" id="pageNumbers">
                                    <!-- 页码将动态生成 -->
                                </div>
                                <button class="btn btn-secondary" id="nextPage" onclick="nextPage()">
                                    下一页 <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增/编辑角色模态框 -->
    <div class="modal" id="roleModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">新增角色</h3>
                <button class="modal-close" onclick="hideRoleModal()">&times;</button>
            </div>
            <form id="roleForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label">角色名称 <span class="required">*</span></label>
                        <input type="text" class="form-control" id="roleName" name="roleName" placeholder="请输入角色名称" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">角色描述</label>
                        <textarea class="form-control" id="roleDescription" name="roleDescription" rows="3" placeholder="请输入角色描述（可选）"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">状态</label>
                        <select class="form-control form-select" id="roleStatus" name="roleStatus">
                            <option value="enabled">启用</option>
                            <option value="disabled">禁用</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="hideRoleModal()">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 确认
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal" id="deleteModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">确认删除</h3>
                <button class="modal-close" onclick="hideDeleteModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="delete-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>确定删除该角色吗？删除后关联用户的权限将失效。</p>
                    <p class="delete-info">将要删除的角色：<strong id="deleteRoleName"></strong></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideDeleteModal()">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                    <i class="fas fa-trash"></i> 确认删除
                </button>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script src="js/role-management.js"></script>
</body>
</html>
