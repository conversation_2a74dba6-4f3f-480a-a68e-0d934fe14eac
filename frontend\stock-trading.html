<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票实时交易模拟 - 金融软件项目管理系统</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/stock-trading.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                <h1>金融软件项目管理系统</h1>
            </div>
            <nav class="nav">
                <a href="index.html" class="btn btn-secondary">
                    <i class="fas fa-home"></i> 返回首页
                </a>
                <div class="user-info">
                    <span>欢迎，交易员</span>
                    <i class="fas fa-user-circle"></i>
                </div>
            </nav>
        </header>

        <main class="main">
            <div class="page-header">
                <h2><i class="fas fa-chart-candlestick"></i> 股票实时交易模拟</h2>
                <div class="market-status">
                    <div class="status-indicator">
                        <div class="status-dot active"></div>
                        <span>市场开放</span>
                    </div>
                    <div class="market-time">
                        <i class="fas fa-clock"></i>
                        <span id="marketTime">2024-01-20 14:30:25</span>
                    </div>
                </div>
            </div>

            <!-- 股票搜索栏 -->
            <div class="search-section">
                <div class="search-container">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="stockSearch" placeholder="输入股票代码或名称..." class="form-control">
                        <button class="btn btn-primary" onclick="searchStock()">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                    </div>
                    <div class="search-suggestions" id="searchSuggestions">
                        <!-- 搜索建议将动态显示 -->
                    </div>
                </div>
                <div class="quick-stocks">
                    <span class="quick-label">热门股票：</span>
                    <div class="quick-stock-list">
                        <button class="quick-stock-btn" onclick="selectStock('000001')">平安银行</button>
                        <button class="quick-stock-btn" onclick="selectStock('000002')">万科A</button>
                        <button class="quick-stock-btn" onclick="selectStock('600036')">招商银行</button>
                        <button class="quick-stock-btn" onclick="selectStock('600519')">贵州茅台</button>
                        <button class="quick-stock-btn" onclick="selectStock('000858')">五粮液</button>
                    </div>
                </div>
            </div>

            <div class="trading-layout">
                <!-- 左侧实时行情表格 -->
                <div class="market-panel">
                    <div class="panel-header">
                        <h3><i class="fas fa-list"></i> 实时行情</h3>
                        <div class="panel-controls">
                            <select class="form-control form-select" id="marketFilter">
                                <option value="all">全部市场</option>
                                <option value="sh">上海市场</option>
                                <option value="sz">深圳市场</option>
                            </select>
                            <div class="refresh-controls">
                                <select class="form-control form-select" id="refreshInterval">
                                    <option value="5">5秒</option>
                                    <option value="10" selected>10秒</option>
                                    <option value="30">30秒</option>
                                </select>
                                <button class="btn btn-secondary" id="pauseRefresh" onclick="toggleRefresh()">
                                    <i class="fas fa-pause"></i> 暂停
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="market-table-container">
                        <table class="table market-table" id="marketTable">
                            <thead>
                                <tr>
                                    <th>代码</th>
                                    <th>名称</th>
                                    <th>最新价</th>
                                    <th>涨跌额</th>
                                    <th>涨跌幅</th>
                                    <th>成交量</th>
                                    <th>成交额</th>
                                    <th>开盘</th>
                                    <th>最高</th>
                                    <th>最低</th>
                                </tr>
                            </thead>
                            <tbody id="marketTableBody">
                                <!-- 行情数据将动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 右侧K线图和盘口 -->
                <div class="chart-panel">
                    <!-- K线图区域 -->
                    <div class="chart-section">
                        <div class="chart-header">
                            <div class="stock-info" id="stockInfo">
                                <div class="stock-name">请选择股票</div>
                                <div class="stock-price">--</div>
                                <div class="stock-change">--</div>
                            </div>
                            <div class="chart-controls">
                                <div class="chart-type-tabs">
                                    <button class="chart-tab active" data-type="realtime" onclick="switchChartType('realtime')">分时图</button>
                                    <button class="chart-tab" data-type="daily" onclick="switchChartType('daily')">日K</button>
                                    <button class="chart-tab" data-type="weekly" onclick="switchChartType('weekly')">周K</button>
                                    <button class="chart-tab" data-type="monthly" onclick="switchChartType('monthly')">月K</button>
                                </div>
                                <div class="chart-tools">
                                    <button class="btn btn-secondary" onclick="fullscreenChart()">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                    <button class="btn btn-secondary" onclick="saveChart()">
                                        <i class="fas fa-camera"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="stockChart"></canvas>
                        </div>
                    </div>

                    <!-- 买五卖五盘口 -->
                    <div class="orderbook-section">
                        <div class="orderbook-header">
                            <h4><i class="fas fa-layer-group"></i> 买五卖五</h4>
                            <div class="orderbook-time" id="orderbookTime">14:30:25</div>
                        </div>
                        <div class="orderbook-container">
                            <div class="orderbook-side sell-side">
                                <div class="side-header">卖盘</div>
                                <div class="orderbook-levels" id="sellLevels">
                                    <!-- 卖盘数据将动态加载 -->
                                </div>
                            </div>
                            <div class="orderbook-middle">
                                <div class="current-price" id="currentPrice">
                                    <div class="price-value">--</div>
                                    <div class="price-change">--</div>
                                </div>
                                <div class="spread-info">
                                    <span>价差：<span id="spread">--</span></span>
                                </div>
                            </div>
                            <div class="orderbook-side buy-side">
                                <div class="side-header">买盘</div>
                                <div class="orderbook-levels" id="buyLevels">
                                    <!-- 买盘数据将动态加载 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 交易信息面板 -->
            <div class="trading-info-panel">
                <div class="info-tabs">
                    <button class="info-tab active" data-tab="trades" onclick="switchInfoTab('trades')">
                        <i class="fas fa-exchange-alt"></i> 成交明细
                    </button>
                    <button class="info-tab" data-tab="news" onclick="switchInfoTab('news')">
                        <i class="fas fa-newspaper"></i> 相关资讯
                    </button>
                    <button class="info-tab" data-tab="analysis" onclick="switchInfoTab('analysis')">
                        <i class="fas fa-chart-bar"></i> 技术分析
                    </button>
                    <button class="info-tab" data-tab="portfolio" onclick="switchInfoTab('portfolio')">
                        <i class="fas fa-briefcase"></i> 我的持仓
                    </button>
                </div>
                <div class="info-content">
                    <div class="tab-content active" id="trades-content">
                        <div class="trades-table-container">
                            <table class="table trades-table">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>价格</th>
                                        <th>数量</th>
                                        <th>方向</th>
                                        <th>金额</th>
                                    </tr>
                                </thead>
                                <tbody id="tradesTableBody">
                                    <!-- 成交明细将动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="tab-content" id="news-content">
                        <div class="news-list" id="newsList">
                            <!-- 资讯列表将动态加载 -->
                        </div>
                    </div>
                    <div class="tab-content" id="analysis-content">
                        <div class="analysis-indicators">
                            <div class="indicator-group">
                                <h5>技术指标</h5>
                                <div class="indicators-grid">
                                    <div class="indicator-item">
                                        <span class="indicator-name">RSI(14)</span>
                                        <span class="indicator-value">65.23</span>
                                    </div>
                                    <div class="indicator-item">
                                        <span class="indicator-name">MACD</span>
                                        <span class="indicator-value">0.15</span>
                                    </div>
                                    <div class="indicator-item">
                                        <span class="indicator-name">KDJ</span>
                                        <span class="indicator-value">72.5</span>
                                    </div>
                                    <div class="indicator-item">
                                        <span class="indicator-name">MA5</span>
                                        <span class="indicator-value">12.85</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-content" id="portfolio-content">
                        <div class="portfolio-summary">
                            <div class="portfolio-stats">
                                <div class="stat-item">
                                    <div class="stat-label">总资产</div>
                                    <div class="stat-value">¥1,000,000.00</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">可用资金</div>
                                    <div class="stat-value">¥500,000.00</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">持仓市值</div>
                                    <div class="stat-value">¥500,000.00</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">今日盈亏</div>
                                    <div class="stat-value profit">+¥12,500.00</div>
                                </div>
                            </div>
                        </div>
                        <div class="portfolio-holdings">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>股票代码</th>
                                        <th>股票名称</th>
                                        <th>持仓数量</th>
                                        <th>成本价</th>
                                        <th>现价</th>
                                        <th>盈亏</th>
                                        <th>盈亏比例</th>
                                    </tr>
                                </thead>
                                <tbody id="portfolioTableBody">
                                    <!-- 持仓数据将动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="js/common.js"></script>
    <script src="js/stock-trading.js"></script>
</body>
</html>
