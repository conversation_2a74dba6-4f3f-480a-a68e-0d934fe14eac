// 通用JavaScript功能

// 模态框管理
class Modal {
    constructor(modalId) {
        this.modal = document.getElementById(modalId);
        this.init();
    }

    init() {
        if (!this.modal) return;
        
        // 绑定关闭事件
        const closeBtn = this.modal.querySelector('.modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.hide());
        }

        // 点击背景关闭
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.hide();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.classList.contains('show')) {
                this.hide();
            }
        });
    }

    show() {
        this.modal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    hide() {
        this.modal.classList.remove('show');
        document.body.style.overflow = '';
    }
}

// 提示消息管理
class Toast {
    static show(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${this.getIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        // 添加样式
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: this.getBackground(type),
            color: 'white',
            padding: '1rem 1.5rem',
            borderRadius: '8px',
            boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)',
            zIndex: '9999',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            maxWidth: '400px',
            wordBreak: 'break-word'
        });

        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }

    static getIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    static getBackground(type) {
        const backgrounds = {
            success: 'linear-gradient(135deg, #28a745, #20c997)',
            error: 'linear-gradient(135deg, #dc3545, #c82333)',
            warning: 'linear-gradient(135deg, #ffc107, #e0a800)',
            info: 'linear-gradient(135deg, #17a2b8, #138496)'
        };
        return backgrounds[type] || backgrounds.info;
    }
}

// 表格管理
class DataTable {
    constructor(tableId, options = {}) {
        this.table = document.getElementById(tableId);
        this.options = {
            pageSize: 10,
            searchable: true,
            sortable: true,
            ...options
        };
        this.currentPage = 1;
        this.data = [];
        this.filteredData = [];
        this.init();
    }

    init() {
        if (!this.table) return;
        
        this.createControls();
        this.bindEvents();
    }

    createControls() {
        const container = this.table.parentNode;
        
        // 搜索框
        if (this.options.searchable) {
            const searchContainer = document.createElement('div');
            searchContainer.className = 'table-search';
            searchContainer.innerHTML = `
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="搜索..." class="form-control search-input">
                </div>
            `;
            container.insertBefore(searchContainer, this.table);
        }

        // 分页控件
        const paginationContainer = document.createElement('div');
        paginationContainer.className = 'table-pagination';
        paginationContainer.innerHTML = `
            <div class="pagination-info">
                <span class="page-info"></span>
            </div>
            <div class="pagination-controls">
                <button class="btn btn-secondary btn-prev" disabled>
                    <i class="fas fa-chevron-left"></i> 上一页
                </button>
                <span class="page-numbers"></span>
                <button class="btn btn-secondary btn-next">
                    下一页 <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        `;
        container.appendChild(paginationContainer);
    }

    bindEvents() {
        // 搜索事件
        const searchInput = this.table.parentNode.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.search(e.target.value);
            });
        }

        // 分页事件
        const prevBtn = this.table.parentNode.querySelector('.btn-prev');
        const nextBtn = this.table.parentNode.querySelector('.btn-next');
        
        if (prevBtn) {
            prevBtn.addEventListener('click', () => this.prevPage());
        }
        if (nextBtn) {
            nextBtn.addEventListener('click', () => this.nextPage());
        }
    }

    setData(data) {
        this.data = data;
        this.filteredData = [...data];
        this.currentPage = 1;
        this.render();
    }

    search(query) {
        if (!query.trim()) {
            this.filteredData = [...this.data];
        } else {
            this.filteredData = this.data.filter(row => 
                Object.values(row).some(value => 
                    String(value).toLowerCase().includes(query.toLowerCase())
                )
            );
        }
        this.currentPage = 1;
        this.render();
    }

    prevPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.render();
        }
    }

    nextPage() {
        const totalPages = Math.ceil(this.filteredData.length / this.options.pageSize);
        if (this.currentPage < totalPages) {
            this.currentPage++;
            this.render();
        }
    }

    render() {
        const startIndex = (this.currentPage - 1) * this.options.pageSize;
        const endIndex = startIndex + this.options.pageSize;
        const pageData = this.filteredData.slice(startIndex, endIndex);

        // 渲染表格数据
        const tbody = this.table.querySelector('tbody');
        if (tbody && pageData.length > 0) {
            tbody.innerHTML = pageData.map(row => this.renderRow(row)).join('');
        }

        // 更新分页信息
        this.updatePagination();
    }

    renderRow(row) {
        // 子类需要重写此方法
        return '';
    }

    updatePagination() {
        const totalPages = Math.ceil(this.filteredData.length / this.options.pageSize);
        const container = this.table.parentNode;
        
        // 更新页面信息
        const pageInfo = container.querySelector('.page-info');
        if (pageInfo) {
            const start = (this.currentPage - 1) * this.options.pageSize + 1;
            const end = Math.min(this.currentPage * this.options.pageSize, this.filteredData.length);
            pageInfo.textContent = `显示 ${start}-${end} 条，共 ${this.filteredData.length} 条`;
        }

        // 更新按钮状态
        const prevBtn = container.querySelector('.btn-prev');
        const nextBtn = container.querySelector('.btn-next');
        
        if (prevBtn) {
            prevBtn.disabled = this.currentPage <= 1;
        }
        if (nextBtn) {
            nextBtn.disabled = this.currentPage >= totalPages;
        }
    }
}

// 表单验证
class FormValidator {
    constructor(formId) {
        this.form = document.getElementById(formId);
        this.rules = {};
        this.init();
    }

    init() {
        if (!this.form) return;
        
        this.form.addEventListener('submit', (e) => {
            if (!this.validate()) {
                e.preventDefault();
            }
        });
    }

    addRule(fieldName, rule) {
        this.rules[fieldName] = rule;
        return this;
    }

    validate() {
        let isValid = true;
        
        for (const [fieldName, rule] of Object.entries(this.rules)) {
            const field = this.form.querySelector(`[name="${fieldName}"]`);
            if (!field) continue;

            const value = field.value.trim();
            let fieldValid = true;
            let errorMessage = '';

            // 必填验证
            if (rule.required && !value) {
                fieldValid = false;
                errorMessage = rule.requiredMessage || '此字段为必填项';
            }

            // 最小长度验证
            if (fieldValid && rule.minLength && value.length < rule.minLength) {
                fieldValid = false;
                errorMessage = rule.minLengthMessage || `最少需要${rule.minLength}个字符`;
            }

            // 正则验证
            if (fieldValid && rule.pattern && !rule.pattern.test(value)) {
                fieldValid = false;
                errorMessage = rule.patternMessage || '格式不正确';
            }

            // 自定义验证
            if (fieldValid && rule.custom) {
                const customResult = rule.custom(value);
                if (customResult !== true) {
                    fieldValid = false;
                    errorMessage = customResult;
                }
            }

            this.showFieldError(field, fieldValid ? '' : errorMessage);
            
            if (!fieldValid) {
                isValid = false;
            }
        }

        return isValid;
    }

    showFieldError(field, message) {
        // 移除旧的错误信息
        const oldError = field.parentNode.querySelector('.field-error');
        if (oldError) {
            oldError.remove();
        }

        if (message) {
            field.classList.add('error');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error';
            errorDiv.textContent = message;
            errorDiv.style.color = '#dc3545';
            errorDiv.style.fontSize = '0.8rem';
            errorDiv.style.marginTop = '0.25rem';
            field.parentNode.appendChild(errorDiv);
        } else {
            field.classList.remove('error');
        }
    }
}

// 工具函数
const Utils = {
    // 格式化数字
    formatNumber(num, decimals = 2) {
        return Number(num).toLocaleString('zh-CN', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    },

    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // 生成随机ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
};

// 全局变量
window.Modal = Modal;
window.Toast = Toast;
window.DataTable = DataTable;
window.FormValidator = FormValidator;
window.Utils = Utils;
