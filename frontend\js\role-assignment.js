// 角色分配管理页面JavaScript

// 模拟用户数据
let usersData = [
    {
        id: 'U001',
        username: '张三',
        email: 'z<PERSON><PERSON>@company.com',
        department: '技术部',
        roles: ['系统管理员'],
        avatar: 'ZS',
        status: 'active',
        lastLogin: '2024-01-20 14:30:00'
    },
    {
        id: 'U002',
        username: '李四',
        email: '<EMAIL>',
        department: '业务部',
        roles: ['普通用户', '数据分析师'],
        avatar: 'LS',
        status: 'active',
        lastLogin: '2024-01-20 11:15:00'
    },
    {
        id: 'U003',
        username: '王五',
        email: '<EMAIL>',
        department: '风控部',
        roles: ['交易员', '风控专员'],
        avatar: 'WW',
        status: 'active',
        lastLogin: '2024-01-20 09:45:00'
    },
    {
        id: 'U004',
        username: '赵六',
        email: 'z<PERSON><PERSON><PERSON>@company.com',
        department: '客服部',
        roles: ['客服代表'],
        avatar: 'ZL',
        status: 'active',
        lastLogin: '2024-01-19 16:20:00'
    },
    {
        id: 'U005',
        username: '钱七',
        email: '<EMAIL>',
        department: '技术部',
        roles: ['普通用户'],
        avatar: 'QQ',
        status: 'inactive',
        lastLogin: '2024-01-18 10:30:00'
    },
    {
        id: 'U006',
        username: '孙八',
        email: '<EMAIL>',
        department: '业务部',
        roles: ['数据分析师', '交易员'],
        avatar: 'SB',
        status: 'active',
        lastLogin: '2024-01-20 13:45:00'
    },
    {
        id: 'U007',
        username: '周九',
        email: '<EMAIL>',
        department: '风控部',
        roles: ['审计员'],
        avatar: 'ZJ',
        status: 'active',
        lastLogin: '2024-01-20 08:30:00'
    },
    {
        id: 'U008',
        username: '吴十',
        email: '<EMAIL>',
        department: '客服部',
        roles: ['普通用户', '客服代表'],
        avatar: 'WS',
        status: 'active',
        lastLogin: '2024-01-19 15:10:00'
    }
];

// 可用角色数据
let availableRoles = [
    {
        id: 'R001',
        name: '系统管理员',
        description: '拥有系统所有权限，可以管理用户、角色和系统配置',
        enabled: true
    },
    {
        id: 'R002',
        name: '普通用户',
        description: '基础用户权限，可以查看股票信息和进行模拟交易',
        enabled: true
    },
    {
        id: 'R003',
        name: '数据分析师',
        description: '可以访问高级数据分析功能和生成报告',
        enabled: true
    },
    {
        id: 'R004',
        name: '交易员',
        description: '专业交易权限，可以执行高级交易操作',
        enabled: true
    },
    {
        id: 'R005',
        name: '审计员',
        description: '审计权限，可以查看系统日志和用户操作记录',
        enabled: false
    },
    {
        id: 'R006',
        name: '客服代表',
        description: '客户服务权限，可以处理用户问题和查询',
        enabled: true
    },
    {
        id: 'R007',
        name: '风控专员',
        description: '风险控制权限，监控交易风险和异常行为',
        enabled: true
    }
];

// 全局变量
let currentUserPage = 1;
const userPageSize = 10;
let filteredUsers = [...usersData];
let selectedUser = null;
let selectedUsers = new Set();
let originalUserRoles = {};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    bindEvents();
    renderUserTable();
    renderBatchRoleCheckboxes();
    updateBatchOperations();
});

// 初始化页面
function initializePage() {
    // 初始化搜索和筛选
    const userSearch = document.getElementById('userSearch');
    const departmentFilter = document.getElementById('departmentFilter');
    const roleFilter = document.getElementById('roleFilter');
    
    userSearch.addEventListener('input', Utils.debounce(handleUserSearch, 300));
    departmentFilter.addEventListener('change', handleUserSearch);
    roleFilter.addEventListener('change', handleUserSearch);
}

// 绑定事件
function bindEvents() {
    // 可以在这里添加其他事件绑定
}

// 处理用户搜索和筛选
function handleUserSearch() {
    const searchTerm = document.getElementById('userSearch').value.toLowerCase();
    const departmentFilter = document.getElementById('departmentFilter').value;
    const roleFilter = document.getElementById('roleFilter').value;
    
    filteredUsers = usersData.filter(user => {
        const matchesSearch = !searchTerm || 
            user.username.toLowerCase().includes(searchTerm) ||
            user.email.toLowerCase().includes(searchTerm);
        
        const matchesDepartment = !departmentFilter || user.department === departmentFilter;
        const matchesRole = !roleFilter || user.roles.includes(roleFilter);
        
        return matchesSearch && matchesDepartment && matchesRole;
    });
    
    currentUserPage = 1;
    selectedUsers.clear();
    updateBatchOperations();
    renderUserTable();
}

// 渲染用户表格
function renderUserTable() {
    const tbody = document.getElementById('usersTableBody');
    const startIndex = (currentUserPage - 1) * userPageSize;
    const endIndex = startIndex + userPageSize;
    const pageData = filteredUsers.slice(startIndex, endIndex);
    
    if (pageData.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <h3>暂无用户数据</h3>
                    <p>没有找到符合条件的用户</p>
                </td>
            </tr>
        `;
    } else {
        tbody.innerHTML = pageData.map(user => `
            <tr class="${selectedUser?.id === user.id ? 'selected' : ''}" 
                onclick="selectUser('${user.id}')">
                <td>${user.id}</td>
                <td>
                    <div class="user-info">
                        <div class="user-avatar">${user.avatar}</div>
                        <div class="user-details">
                            <div class="user-name">${user.username}</div>
                            <div class="user-email">${user.email}</div>
                        </div>
                    </div>
                </td>
                <td>${user.department}</td>
                <td>
                    <div class="role-tags">
                        ${user.roles.map(role => `
                            <span class="role-tag ${getRoleClass(role)}">${role}</span>
                        `).join('')}
                    </div>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="event.stopPropagation(); assignRole('${user.id}')" title="分配角色">
                            <i class="fas fa-user-tag"></i>
                        </button>
                        <button class="btn btn-secondary" onclick="event.stopPropagation(); viewHistory('${user.id}')" title="查看历史">
                            <i class="fas fa-history"></i>
                        </button>
                        <input type="checkbox" ${selectedUsers.has(user.id) ? 'checked' : ''} 
                               onclick="event.stopPropagation(); toggleUserSelection('${user.id}')" 
                               title="选择用户" style="margin-left: 0.5rem;">
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    updateUserPagination();
}

// 获取角色样式类
function getRoleClass(roleName) {
    if (roleName === '系统管理员') return 'admin';
    if (roleName.includes('管理') || roleName.includes('专员')) return 'manager';
    return '';
}

// 更新用户分页
function updateUserPagination() {
    const totalPages = Math.ceil(filteredUsers.length / userPageSize);
    const pageInfo = document.getElementById('userPageInfo');
    const pageNumbers = document.getElementById('userPageNumbers');
    const prevBtn = document.getElementById('userPrevPage');
    const nextBtn = document.getElementById('userNextPage');
    
    // 更新页面信息
    const start = (currentUserPage - 1) * userPageSize + 1;
    const end = Math.min(currentUserPage * userPageSize, filteredUsers.length);
    pageInfo.textContent = `显示 ${start}-${end} 条，共 ${filteredUsers.length} 条`;
    
    // 更新按钮状态
    prevBtn.disabled = currentUserPage <= 1;
    nextBtn.disabled = currentUserPage >= totalPages;
    
    // 生成页码
    let pagesHtml = '';
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentUserPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
        pagesHtml += `
            <a href="#" class="page-number ${i === currentUserPage ? 'active' : ''}" 
               onclick="goToUserPage(${i})">${i}</a>
        `;
    }
    
    pageNumbers.innerHTML = pagesHtml;
}

// 选择用户
function selectUser(userId) {
    const user = usersData.find(u => u.id === userId);
    if (!user) return;
    
    selectedUser = user;
    originalUserRoles[userId] = [...user.roles];
    renderUserTable();
    showUserAssignment(user);
}

// 显示用户角色分配
function showUserAssignment(user) {
    const assignmentContent = document.getElementById('assignmentContent');
    
    assignmentContent.innerHTML = `
        <div class="selected-user-info">
            <div class="selected-user-avatar">${user.avatar}</div>
            <div class="selected-user-details">
                <h4>${user.username}</h4>
                <p>${user.email} | ${user.department}</p>
                <p>最后登录：${user.lastLogin}</p>
            </div>
        </div>
        
        <div class="role-selection-area">
            <h5><i class="fas fa-user-cog"></i> 角色分配</h5>
            <div class="role-checkboxes" id="userRoleCheckboxes">
                ${availableRoles.filter(role => role.enabled).map(role => `
                    <div class="role-checkbox-item ${user.roles.includes(role.name) ? 'checked' : ''}" 
                         onclick="toggleUserRole('${user.id}', '${role.name}')">
                        <input type="checkbox" ${user.roles.includes(role.name) ? 'checked' : ''} 
                               onchange="event.stopPropagation(); toggleUserRole('${user.id}', '${role.name}')">
                        <div class="role-info">
                            <div class="role-name">${role.name}</div>
                            <div class="role-description">${role.description}</div>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
    
    // 启用保存和重置按钮
    document.getElementById('saveBtn').disabled = false;
    document.getElementById('resetBtn').disabled = false;
}

// 切换用户角色
function toggleUserRole(userId, roleName) {
    const user = usersData.find(u => u.id === userId);
    if (!user) return;
    
    const roleIndex = user.roles.indexOf(roleName);
    if (roleIndex > -1) {
        user.roles.splice(roleIndex, 1);
    } else {
        user.roles.push(roleName);
    }
    
    // 更新界面
    showUserAssignment(user);
    renderUserTable();
}

// 切换用户选择（批量操作）
function toggleUserSelection(userId) {
    if (selectedUsers.has(userId)) {
        selectedUsers.delete(userId);
    } else {
        selectedUsers.add(userId);
    }
    updateBatchOperations();
}

// 更新批量操作
function updateBatchOperations() {
    const count = selectedUsers.size;
    document.getElementById('selectedUsersCount').textContent = count;
    
    const batchAssignBtn = document.getElementById('batchAssignBtn');
    const batchRemoveBtn = document.getElementById('batchRemoveBtn');
    
    batchAssignBtn.disabled = count === 0;
    batchRemoveBtn.disabled = count === 0;
}

// 渲染批量角色复选框
function renderBatchRoleCheckboxes() {
    const container = document.getElementById('batchRoleCheckboxes');
    container.innerHTML = availableRoles.filter(role => role.enabled).map(role => `
        <div class="role-checkbox-item" onclick="toggleBatchRole('${role.name}')">
            <input type="checkbox" id="batch_${role.id}" 
                   onchange="event.stopPropagation(); toggleBatchRole('${role.name}')">
            <div class="role-info">
                <div class="role-name">${role.name}</div>
                <div class="role-description">${role.description}</div>
            </div>
        </div>
    `).join('');
}

// 切换批量角色选择
function toggleBatchRole(roleName) {
    const checkbox = document.querySelector(`#batchRoleCheckboxes input[onchange*="${roleName}"]`);
    if (checkbox) {
        checkbox.checked = !checkbox.checked;
        checkbox.parentElement.classList.toggle('checked', checkbox.checked);
    }
}

// 分页函数
function prevUserPage() {
    if (currentUserPage > 1) {
        currentUserPage--;
        renderUserTable();
    }
}

function nextUserPage() {
    const totalPages = Math.ceil(filteredUsers.length / userPageSize);
    if (currentUserPage < totalPages) {
        currentUserPage++;
        renderUserTable();
    }
}

function goToUserPage(page) {
    currentUserPage = page;
    renderUserTable();
}

// 分配角色（单个用户）
function assignRole(userId) {
    selectUser(userId);
}

// 保存分配
function saveAssignment() {
    if (!selectedUser) return;
    
    // 模拟保存到服务器
    delete originalUserRoles[selectedUser.id];
    
    Toast.show(`用户 "${selectedUser.username}" 的角色分配已保存！`, 'success');
    
    // 可以在这里添加保存到服务器的逻辑
}

// 重置分配
function resetAssignment() {
    if (!selectedUser || !originalUserRoles[selectedUser.id]) return;
    
    selectedUser.roles = [...originalUserRoles[selectedUser.id]];
    showUserAssignment(selectedUser);
    renderUserTable();
    
    Toast.show('角色分配已重置！', 'info');
}

// 批量分配角色
function batchAssignRoles() {
    const selectedRoles = Array.from(document.querySelectorAll('#batchRoleCheckboxes input:checked'))
        .map(checkbox => checkbox.parentElement.querySelector('.role-name').textContent);
    
    if (selectedRoles.length === 0) {
        Toast.show('请选择要分配的角色！', 'warning');
        return;
    }
    
    let assignedCount = 0;
    selectedUsers.forEach(userId => {
        const user = usersData.find(u => u.id === userId);
        if (user) {
            selectedRoles.forEach(role => {
                if (!user.roles.includes(role)) {
                    user.roles.push(role);
                    assignedCount++;
                }
            });
        }
    });
    
    renderUserTable();
    if (selectedUser && selectedUsers.has(selectedUser.id)) {
        showUserAssignment(selectedUser);
    }
    
    Toast.show(`成功为 ${selectedUsers.size} 个用户分配了角色！`, 'success');
}

// 批量移除角色
function batchRemoveRoles() {
    const selectedRoles = Array.from(document.querySelectorAll('#batchRoleCheckboxes input:checked'))
        .map(checkbox => checkbox.parentElement.querySelector('.role-name').textContent);
    
    if (selectedRoles.length === 0) {
        Toast.show('请选择要移除的角色！', 'warning');
        return;
    }
    
    let removedCount = 0;
    selectedUsers.forEach(userId => {
        const user = usersData.find(u => u.id === userId);
        if (user) {
            selectedRoles.forEach(role => {
                const roleIndex = user.roles.indexOf(role);
                if (roleIndex > -1) {
                    user.roles.splice(roleIndex, 1);
                    removedCount++;
                }
            });
        }
    });
    
    renderUserTable();
    if (selectedUser && selectedUsers.has(selectedUser.id)) {
        showUserAssignment(selectedUser);
    }
    
    Toast.show(`成功移除了 ${removedCount} 个角色分配！`, 'success');
}

// 清除用户选择
function clearUserSelection() {
    selectedUsers.clear();
    updateBatchOperations();
    renderUserTable();
    
    // 清除批量角色选择
    document.querySelectorAll('#batchRoleCheckboxes input').forEach(checkbox => {
        checkbox.checked = false;
        checkbox.parentElement.classList.remove('checked');
    });
    
    Toast.show('已清除所有选择！', 'info');
}

// 查看分配历史
function viewHistory(userId) {
    const user = usersData.find(u => u.id === userId);
    if (!user) return;
    
    // 模拟历史数据
    const historyData = [
        {
            action: '分配角色：数据分析师',
            time: '2024-01-20 10:30:00',
            operator: '系统管理员'
        },
        {
            action: '移除角色：普通用户',
            time: '2024-01-19 14:15:00',
            operator: '系统管理员'
        },
        {
            action: '分配角色：普通用户',
            time: '2024-01-15 09:00:00',
            operator: '系统管理员'
        }
    ];
    
    const historyContent = document.getElementById('historyContent');
    historyContent.innerHTML = `
        <h4>用户：${user.username} (${user.email})</h4>
        <div class="history-list">
            ${historyData.map(item => `
                <div class="history-item">
                    <div class="history-icon">
                        <i class="fas fa-user-cog"></i>
                    </div>
                    <div class="history-details">
                        <div class="history-action">${item.action}</div>
                        <div class="history-time">${item.time} | 操作人：${item.operator}</div>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
    
    new Modal('assignmentHistoryModal').show();
}

// 隐藏分配历史
function hideAssignmentHistory() {
    new Modal('assignmentHistoryModal').hide();
}

// 刷新用户列表
function refreshUsers() {
    // 模拟刷新数据
    handleUserSearch();
    Toast.show('用户列表已刷新！', 'info');
}

// 导出用户数据
function exportUsers() {
    const csvContent = "data:text/csv;charset=utf-8," 
        + "用户ID,用户名,邮箱,部门,角色,状态,最后登录\n"
        + filteredUsers.map(user => 
            `${user.id},${user.username},${user.email},${user.department},"${user.roles.join(';')}",${user.status},${user.lastLogin}`
        ).join("\n");
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `用户角色数据_${Utils.formatDate(new Date(), 'YYYY-MM-DD')}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    Toast.show('用户数据导出成功！', 'success');
}
