/* 角色分配管理页面样式 */
.page-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.page-header h2 {
    color: #2c3e50;
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.page-header h2 i {
    color: #4a90e2;
}

.page-header p {
    color: #666;
    font-size: 1.1rem;
}

/* 内容布局 */
.content-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    margin-bottom: 2rem;
}

/* 面板通用样式 */
.user-list-panel,
.role-assignment-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: fit-content;
}

.panel-header {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    margin: 0;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.panel-actions {
    display: flex;
    gap: 0.5rem;
}

.panel-actions .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

.panel-actions .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

/* 搜索和筛选区域 */
.search-filter-section {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.search-box {
    position: relative;
    margin-bottom: 1rem;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    z-index: 1;
}

.search-box input {
    padding-left: 2.5rem;
}

.filter-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

/* 用户表格容器 */
.user-table-container {
    max-height: 500px;
    overflow-y: auto;
}

.user-table-container .table {
    margin: 0;
}

.user-table-container .table th {
    position: sticky;
    top: 0;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    z-index: 10;
}

.user-table-container .table tbody tr {
    cursor: pointer;
    transition: all 0.3s ease;
}

.user-table-container .table tbody tr:hover {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
}

.user-table-container .table tbody tr.selected {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-left: 4px solid #4a90e2;
}

/* 用户信息样式 */
.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #4a90e2, #357abd);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

.user-details .user-name {
    font-weight: 600;
    color: #2c3e50;
}

.user-details .user-email {
    font-size: 0.8rem;
    color: #666;
}

/* 角色标签 */
.role-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.role-tag {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    color: #1976d2;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    border: 1px solid #bbdefb;
}

.role-tag.admin {
    background: linear-gradient(135deg, #ffebee, #ffcdd2);
    color: #c62828;
    border-color: #ffcdd2;
}

.role-tag.manager {
    background: linear-gradient(135deg, #f3e5f5, #e1bee7);
    color: #7b1fa2;
    border-color: #e1bee7;
}

/* 角色分配区域 */
.assignment-content {
    padding: 2rem;
    min-height: 400px;
}

.welcome-message {
    text-align: center;
    color: #666;
    padding: 3rem 2rem;
}

.welcome-message i {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 1rem;
}

.welcome-message h4 {
    margin-bottom: 0.5rem;
    color: #999;
}

.selected-user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 12px;
    border: 2px solid #e9ecef;
}

.selected-user-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #4a90e2, #357abd);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
}

.selected-user-details h4 {
    margin: 0 0 0.25rem 0;
    color: #2c3e50;
    font-size: 1.25rem;
}

.selected-user-details p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

/* 角色选择区域 */
.role-selection-area {
    margin-top: 1.5rem;
}

.role-selection-area h5 {
    color: #2c3e50;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.role-selection-area h5 i {
    color: #4a90e2;
}

.role-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.role-checkbox-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.role-checkbox-item:hover {
    border-color: #4a90e2;
    background: linear-gradient(135deg, #f8f9fa, #e3f2fd);
}

.role-checkbox-item.checked {
    border-color: #4a90e2;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
}

.role-checkbox-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #4a90e2;
}

.role-info .role-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.role-info .role-description {
    font-size: 0.8rem;
    color: #666;
    line-height: 1.3;
}

/* 批量分配面板 */
.batch-assignment-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    max-width: 1400px;
    margin: 0 auto;
}

.batch-assignment-panel .panel-header {
    background: none;
    color: #2c3e50;
    padding: 0 0 1rem 0;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 1.5rem;
}

.batch-assignment-panel .panel-header h3 {
    color: #2c3e50;
}

.batch-assignment-panel .panel-header h3 i {
    color: #4a90e2;
}

.selected-users-count {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.batch-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 2rem;
    align-items: start;
}

.batch-role-selection .form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
    display: block;
}

.batch-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    min-width: 200px;
}

/* 分页样式 */
.table-pagination {
    background: #f8f9fa;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #dee2e6;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-numbers {
    display: flex;
    gap: 0.25rem;
}

.page-number {
    padding: 0.5rem 0.75rem;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.page-number:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.page-number.active {
    background: #4a90e2;
    border-color: #4a90e2;
    color: white;
}

/* 历史记录样式 */
.history-content {
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.history-item:last-child {
    border-bottom: none;
}

.history-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #4a90e2, #357abd);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.history-details .history-action {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.history-details .history-time {
    font-size: 0.8rem;
    color: #666;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .content-layout {
        gap: 1.5rem;
    }
    
    .batch-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .batch-actions {
        flex-direction: row;
        flex-wrap: wrap;
    }
}

@media (max-width: 768px) {
    .content-layout {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .role-assignment-panel {
        order: 1;
    }
    
    .user-list-panel {
        order: 2;
    }
    
    .panel-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .filter-row {
        grid-template-columns: 1fr;
    }
    
    .role-checkboxes {
        grid-template-columns: 1fr;
    }
    
    .selected-user-info {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 1.5rem 1rem;
    }
    
    .page-header h2 {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .batch-actions {
        flex-direction: column;
    }
    
    .batch-actions .btn {
        width: 100%;
    }
}

/* 动画效果 */
.role-checkbox-item {
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.selected-user-info {
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 滚动条样式 */
.user-table-container::-webkit-scrollbar,
.history-content::-webkit-scrollbar {
    width: 6px;
}

.user-table-container::-webkit-scrollbar-track,
.history-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.user-table-container::-webkit-scrollbar-thumb,
.history-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.user-table-container::-webkit-scrollbar-thumb:hover,
.history-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
