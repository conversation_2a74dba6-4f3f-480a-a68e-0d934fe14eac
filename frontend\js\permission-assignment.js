// 权限分配管理页面JavaScript

// 角色数据（从角色管理页面复用）
let rolesData = [
    {
        id: 'R001',
        name: '系统管理员',
        description: '拥有系统所有权限，可以管理用户、角色和系统配置',
        userCount: 3,
        permissions: ['F001', 'F001001', 'F001001001', 'F001001002', 'F001001003', 'F001002', 'F001002001', 'F001002002', 'F002', 'F002001', 'F002002', 'F002003', 'F002004']
    },
    {
        id: 'R002',
        name: '普通用户',
        description: '基础用户权限，可以查看股票信息和进行模拟交易',
        userCount: 156,
        permissions: ['F001001', 'F001001001', 'F001001002', 'F001001003', 'F001002', 'F001002001']
    },
    {
        id: 'R003',
        name: '数据分析师',
        description: '可以访问高级数据分析功能和生成报告',
        userCount: 12,
        permissions: ['F001001', 'F001001003', 'F001002', 'F001002001', 'F001002002']
    },
    {
        id: 'R004',
        name: '交易员',
        description: '专业交易权限，可以执行高级交易操作',
        userCount: 28,
        permissions: ['F001001', 'F001001001', 'F001001002', 'F001001003']
    },
    {
        id: 'R005',
        name: '审计员',
        description: '审计权限，可以查看系统日志和用户操作记录',
        userCount: 5,
        permissions: ['F001002', 'F001002001', 'F001002002']
    },
    {
        id: 'R006',
        name: '客服代表',
        description: '客户服务权限，可以处理用户问题和查询',
        userCount: 15,
        permissions: ['F001002', 'F001002001']
    },
    {
        id: 'R007',
        name: '风控专员',
        description: '风险控制权限，监控交易风险和异常行为',
        userCount: 8,
        permissions: ['F001001', 'F001001003', 'F001002', 'F001002002']
    }
];

// 权限功能数据（从功能管理页面复用）
let permissionsData = [
    {
        id: 'F001',
        name: '业务子系统',
        code: 'business_system',
        icon: 'fas fa-briefcase',
        children: [
            {
                id: 'F001001',
                name: '股票买卖',
                code: 'stock_trading',
                icon: 'fas fa-chart-line',
                children: [
                    {
                        id: 'F001001001',
                        name: '买入操作',
                        code: 'stock_buy',
                        icon: 'fas fa-arrow-up'
                    },
                    {
                        id: 'F001001002',
                        name: '卖出操作',
                        code: 'stock_sell',
                        icon: 'fas fa-arrow-down'
                    },
                    {
                        id: 'F001001003',
                        name: '实时行情',
                        code: 'stock_realtime',
                        icon: 'fas fa-chart-line'
                    }
                ]
            },
            {
                id: 'F001002',
                name: '公司信息查询',
                code: 'company_info',
                icon: 'fas fa-building',
                children: [
                    {
                        id: 'F001002001',
                        name: '基本信息',
                        code: 'company_basic',
                        icon: 'fas fa-info-circle'
                    },
                    {
                        id: 'F001002002',
                        name: '财务数据',
                        code: 'company_finance',
                        icon: 'fas fa-chart-bar'
                    }
                ]
            }
        ]
    },
    {
        id: 'F002',
        name: '用户权限子系统',
        code: 'permission_system',
        icon: 'fas fa-shield-alt',
        children: [
            {
                id: 'F002001',
                name: '角色管理',
                code: 'role_management',
                icon: 'fas fa-users-cog'
            },
            {
                id: 'F002002',
                name: '功能管理',
                code: 'function_management',
                icon: 'fas fa-cogs'
            },
            {
                id: 'F002003',
                name: '角色分配管理',
                code: 'role_assignment',
                icon: 'fas fa-user-tag'
            },
            {
                id: 'F002004',
                name: '权限分配管理',
                code: 'permission_assignment',
                icon: 'fas fa-key'
            }
        ]
    }
];

// 全局变量
let selectedRole = null;
let expandedSystems = new Set();
let originalPermissions = {};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    bindEvents();
    renderRoleList();
    renderCompareRoleOptions();
    updatePermissionStats();
});

// 初始化页面
function initializePage() {
    // 初始化搜索
    const roleSearch = document.getElementById('roleSearch');
    roleSearch.addEventListener('input', Utils.debounce(handleRoleSearch, 300));
    
    // 初始化批量操作类型切换
    const batchOperationType = document.getElementById('batchOperationType');
    batchOperationType.addEventListener('change', handleBatchOperationTypeChange);
}

// 绑定事件
function bindEvents() {
    // 可以在这里添加其他事件绑定
}

// 渲染角色列表
function renderRoleList() {
    const roleList = document.getElementById('roleList');
    
    roleList.innerHTML = rolesData.map(role => `
        <div class="role-item ${selectedRole?.id === role.id ? 'selected' : ''}" 
             onclick="selectRole('${role.id}')">
            <div class="role-icon">
                <i class="fas fa-user-shield"></i>
            </div>
            <div class="role-info">
                <div class="role-name">${role.name}</div>
                <div class="role-description">${role.description}</div>
                <div class="role-users">${role.userCount} 个用户</div>
            </div>
        </div>
    `).join('');
}

// 处理角色搜索
function handleRoleSearch() {
    const searchTerm = document.getElementById('roleSearch').value.toLowerCase();
    
    if (!searchTerm) {
        renderRoleList();
        return;
    }
    
    const filteredRoles = rolesData.filter(role => 
        role.name.toLowerCase().includes(searchTerm) ||
        role.description.toLowerCase().includes(searchTerm)
    );
    
    const roleList = document.getElementById('roleList');
    roleList.innerHTML = filteredRoles.map(role => `
        <div class="role-item ${selectedRole?.id === role.id ? 'selected' : ''}" 
             onclick="selectRole('${role.id}')">
            <div class="role-icon">
                <i class="fas fa-user-shield"></i>
            </div>
            <div class="role-info">
                <div class="role-name">${role.name}</div>
                <div class="role-description">${role.description}</div>
                <div class="role-users">${role.userCount} 个用户</div>
            </div>
        </div>
    `).join('');
}

// 选择角色
function selectRole(roleId) {
    const role = rolesData.find(r => r.id === roleId);
    if (!role) return;
    
    selectedRole = role;
    originalPermissions[roleId] = [...role.permissions];
    
    renderRoleList();
    showPermissionConfig(role);
    updatePermissionStats();
    
    // 启用保存按钮
    document.getElementById('savePermissionsBtn').disabled = false;
}

// 显示权限配置
function showPermissionConfig(role) {
    const permissionContent = document.getElementById('permissionContent');
    
    permissionContent.innerHTML = `
        <div class="selected-role-info">
            <div class="role-icon">
                <i class="fas fa-user-shield"></i>
            </div>
            <div class="role-details">
                <h4>${role.name}</h4>
                <p>${role.description}</p>
                <p>当前用户数：${role.userCount}</p>
            </div>
        </div>
        
        <div class="permission-tree" id="permissionTree">
            ${renderPermissionTree()}
        </div>
    `;
}

// 渲染权限树
function renderPermissionTree() {
    return permissionsData.map(system => {
        const isExpanded = expandedSystems.has(system.id);
        const systemPermissions = getAllSystemPermissions(system);
        const assignedCount = systemPermissions.filter(p => selectedRole.permissions.includes(p)).length;
        const totalCount = systemPermissions.length;
        const isSystemChecked = assignedCount === totalCount;
        const isSystemIndeterminate = assignedCount > 0 && assignedCount < totalCount;
        
        return `
            <div class="permission-system">
                <div class="system-header" onclick="toggleSystem('${system.id}')">
                    <div class="system-toggle ${isExpanded ? 'expanded' : ''}">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                    <input type="checkbox" class="system-checkbox" 
                           ${isSystemChecked ? 'checked' : ''} 
                           ${isSystemIndeterminate ? 'indeterminate' : ''}
                           onchange="event.stopPropagation(); toggleSystemPermission('${system.id}')" />
                    <i class="system-icon ${system.icon}"></i>
                    <div class="system-info">
                        <div class="system-name">${system.name}</div>
                        <div class="system-count">${assignedCount}/${totalCount} 个权限</div>
                    </div>
                </div>
                <div class="permission-functions ${isExpanded ? 'expanded' : ''}">
                    ${renderFunctionPermissions(system.children || [])}
                </div>
            </div>
        `;
    }).join('');
}

// 渲染功能权限
function renderFunctionPermissions(functions) {
    return functions.map(func => {
        const isChecked = selectedRole.permissions.includes(func.id);
        
        let content = `
            <div class="function-item ${isChecked ? 'checked' : ''}" 
                 onclick="toggleFunctionPermission('${func.id}')">
                <input type="checkbox" class="function-checkbox" 
                       ${isChecked ? 'checked' : ''} 
                       onchange="event.stopPropagation(); toggleFunctionPermission('${func.id}')" />
                <i class="function-icon ${func.icon}"></i>
                <div class="function-info">
                    <div class="function-name">${func.name}</div>
                    <div class="function-code">${func.code}</div>
                </div>
                <div class="function-actions">
                    <button class="btn btn-secondary" onclick="event.stopPropagation(); showPermissionDetail('${func.id}')" title="查看详情">
                        <i class="fas fa-info"></i>
                    </button>
                </div>
            </div>
        `;
        
        // 如果有子功能，递归渲染
        if (func.children && func.children.length > 0) {
            content += `
                <div style="margin-left: 2rem; border-left: 1px solid #e9ecef; padding-left: 1rem;">
                    ${renderFunctionPermissions(func.children)}
                </div>
            `;
        }
        
        return content;
    }).join('');
}

// 获取系统所有权限ID
function getAllSystemPermissions(system) {
    let permissions = [system.id];
    
    const collectPermissions = (functions) => {
        functions.forEach(func => {
            permissions.push(func.id);
            if (func.children) {
                collectPermissions(func.children);
            }
        });
    };
    
    if (system.children) {
        collectPermissions(system.children);
    }
    
    return permissions;
}

// 切换系统展开/收起
function toggleSystem(systemId) {
    if (expandedSystems.has(systemId)) {
        expandedSystems.delete(systemId);
    } else {
        expandedSystems.add(systemId);
    }
    showPermissionConfig(selectedRole);
}

// 切换系统权限
function toggleSystemPermission(systemId) {
    if (!selectedRole) return;
    
    const system = permissionsData.find(s => s.id === systemId);
    if (!system) return;
    
    const systemPermissions = getAllSystemPermissions(system);
    const assignedCount = systemPermissions.filter(p => selectedRole.permissions.includes(p)).length;
    const totalCount = systemPermissions.length;
    
    if (assignedCount === totalCount) {
        // 全部取消
        systemPermissions.forEach(permId => {
            const index = selectedRole.permissions.indexOf(permId);
            if (index > -1) {
                selectedRole.permissions.splice(index, 1);
            }
        });
    } else {
        // 全部选中
        systemPermissions.forEach(permId => {
            if (!selectedRole.permissions.includes(permId)) {
                selectedRole.permissions.push(permId);
            }
        });
    }
    
    showPermissionConfig(selectedRole);
    updatePermissionStats();
}

// 切换功能权限
function toggleFunctionPermission(functionId) {
    if (!selectedRole) return;
    
    const index = selectedRole.permissions.indexOf(functionId);
    if (index > -1) {
        selectedRole.permissions.splice(index, 1);
    } else {
        selectedRole.permissions.push(functionId);
    }
    
    showPermissionConfig(selectedRole);
    updatePermissionStats();
}

// 更新权限统计
function updatePermissionStats() {
    const totalPermissions = getAllPermissionIds().length;
    const assignedPermissions = selectedRole ? selectedRole.permissions.length : 0;
    const unassignedPermissions = totalPermissions - assignedPermissions;
    
    document.getElementById('totalPermissions').textContent = totalPermissions;
    document.getElementById('assignedPermissions').textContent = assignedPermissions;
    document.getElementById('unassignedPermissions').textContent = unassignedPermissions;
}

// 获取所有权限ID
function getAllPermissionIds() {
    let allIds = [];
    
    const collectIds = (items) => {
        items.forEach(item => {
            allIds.push(item.id);
            if (item.children) {
                collectIds(item.children);
            }
        });
    };
    
    collectIds(permissionsData);
    return allIds;
}

// 全选权限
function selectAll() {
    if (!selectedRole) return;
    
    selectedRole.permissions = [...getAllPermissionIds()];
    showPermissionConfig(selectedRole);
    updatePermissionStats();
    
    Toast.show('已选择所有权限！', 'success');
}

// 取消全选
function unselectAll() {
    if (!selectedRole) return;
    
    selectedRole.permissions = [];
    showPermissionConfig(selectedRole);
    updatePermissionStats();
    
    Toast.show('已取消所有权限选择！', 'info');
}

// 保存权限配置
function savePermissions() {
    if (!selectedRole) return;
    
    // 模拟保存到服务器
    delete originalPermissions[selectedRole.id];
    
    Toast.show(`角色 "${selectedRole.name}" 的权限配置已保存！`, 'success');
    
    // 可以在这里添加保存到服务器的逻辑
}

// 渲染对比角色选项
function renderCompareRoleOptions() {
    const compareRole1 = document.getElementById('compareRole1');
    const compareRole2 = document.getElementById('compareRole2');
    
    const options = rolesData.map(role => 
        `<option value="${role.id}">${role.name}</option>`
    ).join('');
    
    compareRole1.innerHTML = '<option value="">选择角色1</option>' + options;
    compareRole2.innerHTML = '<option value="">选择角色2</option>' + options;
}

// 对比权限
function comparePermissions() {
    const role1Id = document.getElementById('compareRole1').value;
    const role2Id = document.getElementById('compareRole2').value;
    
    if (!role1Id || !role2Id) {
        Toast.show('请选择两个角色进行对比！', 'warning');
        return;
    }
    
    if (role1Id === role2Id) {
        Toast.show('请选择不同的角色进行对比！', 'warning');
        return;
    }
    
    const role1 = rolesData.find(r => r.id === role1Id);
    const role2 = rolesData.find(r => r.id === role2Id);
    
    const compareContent = document.getElementById('compareContent');
    compareContent.innerHTML = `
        <div class="compare-result">
            <div class="compare-role">
                <h4><i class="fas fa-user"></i> ${role1.name}</h4>
                <div class="compare-permissions">
                    ${renderComparePermissions(role1, role2)}
                </div>
            </div>
            <div class="compare-role">
                <h4><i class="fas fa-user"></i> ${role2.name}</h4>
                <div class="compare-permissions">
                    ${renderComparePermissions(role2, role1)}
                </div>
            </div>
        </div>
    `;
}

// 渲染对比权限
function renderComparePermissions(currentRole, otherRole) {
    const allPermissions = getAllPermissionIds();
    
    return allPermissions.map(permId => {
        const permission = findPermissionById(permId);
        if (!permission) return '';
        
        const currentHas = currentRole.permissions.includes(permId);
        const otherHas = otherRole.permissions.includes(permId);
        
        let className = '';
        let icon = '';
        
        if (currentHas && otherHas) {
            className = 'common-permission';
            icon = 'fas fa-check-circle';
        } else if (currentHas && !otherHas) {
            className = 'has-permission';
            icon = 'fas fa-plus-circle';
        } else {
            className = 'no-permission';
            icon = 'fas fa-minus-circle';
        }
        
        return `
            <div class="compare-permission-item ${className}">
                <i class="${icon}"></i>
                <span>${permission.name}</span>
            </div>
        `;
    }).join('');
}

// 查找权限信息
function findPermissionById(permId) {
    const findInArray = (items) => {
        for (const item of items) {
            if (item.id === permId) {
                return item;
            }
            if (item.children) {
                const found = findInArray(item.children);
                if (found) return found;
            }
        }
        return null;
    };
    
    return findInArray(permissionsData);
}

// 显示权限详情
function showPermissionDetail(permissionId) {
    const permission = findPermissionById(permissionId);
    if (!permission) return;
    
    const detailContent = document.getElementById('permissionDetailContent');
    detailContent.innerHTML = `
        <div class="permission-detail">
            <div class="detail-header">
                <i class="${permission.icon}" style="font-size: 2rem; color: #4a90e2; margin-right: 1rem;"></i>
                <div>
                    <h4>${permission.name}</h4>
                    <p style="color: #666; margin: 0;">${permission.code}</p>
                </div>
            </div>
            <div class="detail-info" style="margin-top: 1.5rem;">
                <p><strong>权限ID：</strong>${permission.id}</p>
                <p><strong>权限代码：</strong>${permission.code}</p>
                <p><strong>权限名称：</strong>${permission.name}</p>
                <p><strong>图标：</strong>${permission.icon}</p>
                ${permission.url ? `<p><strong>访问路径：</strong>${permission.url}</p>` : ''}
                ${permission.description ? `<p><strong>描述：</strong>${permission.description}</p>` : ''}
            </div>
            <div class="assigned-roles" style="margin-top: 1.5rem;">
                <h5>拥有此权限的角色：</h5>
                <div class="role-tags">
                    ${rolesData.filter(role => role.permissions.includes(permissionId))
                        .map(role => `<span class="role-tag">${role.name}</span>`).join('')}
                </div>
            </div>
        </div>
    `;
    
    new Modal('permissionDetailModal').show();
}

// 隐藏权限详情
function hidePermissionDetail() {
    new Modal('permissionDetailModal').hide();
}

// 处理批量操作类型变化
function handleBatchOperationTypeChange() {
    const operationType = document.getElementById('batchOperationType').value;
    const sourceRoleGroup = document.getElementById('sourceRoleGroup');
    const permissionSelectionGroup = document.getElementById('permissionSelectionGroup');
    
    if (operationType === 'copy') {
        sourceRoleGroup.style.display = 'block';
        permissionSelectionGroup.style.display = 'none';
        
        // 填充源角色选项
        const sourceRole = document.getElementById('sourceRole');
        sourceRole.innerHTML = '<option value="">选择源角色</option>' + 
            rolesData.map(role => `<option value="${role.id}">${role.name}</option>`).join('');
    } else {
        sourceRoleGroup.style.display = 'none';
        permissionSelectionGroup.style.display = 'block';
    }
}

// 刷新角色列表
function refreshRoles() {
    renderRoleList();
    renderCompareRoleOptions();
    Toast.show('角色列表已刷新！', 'info');
}

// 导出权限配置
function exportPermissions() {
    const csvContent = "data:text/csv;charset=utf-8," 
        + "角色ID,角色名称,权限ID,权限名称,权限代码\n"
        + rolesData.flatMap(role => 
            role.permissions.map(permId => {
                const permission = findPermissionById(permId);
                return `${role.id},${role.name},${permId},${permission ? permission.name : ''},${permission ? permission.code : ''}`;
            })
        ).join("\n");
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `权限配置_${Utils.formatDate(new Date(), 'YYYY-MM-DD')}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    Toast.show('权限配置导出成功！', 'success');
}
