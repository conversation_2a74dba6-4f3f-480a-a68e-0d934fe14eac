/* 股票买卖操作页面样式 */
.page-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1.5rem 2rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-header h2 {
    color: #2c3e50;
    font-size: 1.75rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-header h2 i {
    color: #4a90e2;
}

.account-summary {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.account-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.account-label {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.account-value {
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.account-value.available-funds {
    color: #28a745;
}

.account-value.profit {
    color: #dc3545;
}

.account-value.loss {
    color: #28a745;
}

/* 操作布局 */
.operations-layout {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

/* 面板通用样式 */
.stock-info-panel,
.trading-form-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.panel-header {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    margin: 0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.panel-actions {
    display: flex;
    gap: 0.5rem;
}

.panel-actions .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

.panel-actions .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

/* 股票信息面板 */
.stock-search-section {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.search-box {
    position: relative;
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    z-index: 1;
}

.search-box input {
    flex: 1;
    padding-left: 2.5rem;
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: none;
    max-height: 300px;
    overflow-y: auto;
    margin-top: 0.5rem;
}

.suggestion-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: background 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.suggestion-item:hover {
    background: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.current-stock-info {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    min-height: 200px;
}

.no-stock-selected {
    text-align: center;
    color: #666;
    padding: 2rem 1rem;
}

.no-stock-selected i {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 1rem;
}

.no-stock-selected h4 {
    margin-bottom: 0.5rem;
    color: #999;
}

.stock-detail-info {
    display: grid;
    gap: 1rem;
}

.stock-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.stock-title {
    display: flex;
    flex-direction: column;
}

.stock-code {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
}

.stock-name {
    font-size: 1rem;
    color: #666;
    margin-top: 0.25rem;
}

.stock-price-info {
    text-align: right;
}

.current-price {
    font-family: 'Courier New', monospace;
    font-size: 1.5rem;
    font-weight: 700;
}

.price-change {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

.price-up {
    color: #dc3545;
}

.price-down {
    color: #28a745;
}

.price-equal {
    color: #6c757d;
}

.stock-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
}

.stat-value {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #2c3e50;
}

/* 快速股票按钮 */
.quick-stock-buttons {
    padding: 1.5rem;
}

.quick-stock-buttons h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quick-stock-buttons h4 i {
    color: #ffc107;
}

.quick-buttons-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.quick-stock-btn {
    padding: 1rem;
    border: 1px solid #e9ecef;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.quick-stock-btn:hover {
    border-color: #4a90e2;
    background: #f0f7ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.2);
}

.quick-stock-btn .stock-code {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.quick-stock-btn .stock-name {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.quick-stock-btn .stock-price {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    font-size: 0.9rem;
}

/* 交易表单面板 */
.trade-type-switcher {
    display: flex;
    border-radius: 6px;
    overflow: hidden;
}

.trade-type-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.trade-type-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.trade-type-btn.active.buy-btn {
    background: #dc3545;
}

.trade-type-btn.active.sell-btn {
    background: #28a745;
}

.trading-form-content {
    padding: 2rem;
}

.trade-form {
    display: none;
}

.trade-form.active {
    display: block;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #495057;
}

.required {
    color: #dc3545;
}

.stock-input-group {
    display: flex;
    gap: 0.5rem;
}

.stock-input-group .form-control {
    flex: 1;
}

.quantity-input-group {
    position: relative;
}

.input-hint {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.8rem;
    color: #999;
    pointer-events: none;
}

.quantity-buttons {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.75rem;
    flex-wrap: wrap;
}

.quantity-buttons .btn {
    flex: 1;
    min-width: 80px;
    font-size: 0.85rem;
    padding: 0.5rem 0.75rem;
}

.price-type-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.radio-group {
    display: flex;
    gap: 1.5rem;
}

.radio-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.radio-item input[type="radio"] {
    width: 18px;
    height: 18px;
    accent-color: #4a90e2;
}

.radio-label {
    font-weight: 500;
    color: #495057;
}

.limit-price-input {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.funds-info,
.holding-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid #e9ecef;
}

.funds-item,
.holding-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.funds-item:last-child,
.holding-item:last-child {
    margin-bottom: 0;
}

.funds-label,
.holding-label {
    color: #666;
    font-size: 0.9rem;
}

.funds-value,
.holding-value {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #2c3e50;
}

.btn-large {
    width: 100%;
    padding: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 8px;
}

.btn-large:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 交易结果面板 */
.trading-result-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.trading-results {
    padding: 1.5rem;
    max-height: 400px;
    overflow-y: auto;
}

.no-results {
    text-align: center;
    color: #666;
    padding: 3rem 2rem;
}

.no-results i {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 1rem;
}

.result-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.result-item:hover {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.result-item:last-child {
    margin-bottom: 0;
}

.result-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.result-icon.success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.result-icon.error {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.result-content {
    flex: 1;
}

.result-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.result-details {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.result-time {
    color: #999;
    font-size: 0.8rem;
    font-family: 'Courier New', monospace;
}

/* 股票选择器模态框 */
.stock-selector-search {
    margin-bottom: 1rem;
}

.stock-selector-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 6px;
}

.stock-selector-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: background 0.3s ease;
}

.stock-selector-item:hover {
    background: #f8f9fa;
}

.stock-selector-item:last-child {
    border-bottom: none;
}

.stock-info-left {
    display: flex;
    flex-direction: column;
}

.stock-info-right {
    text-align: right;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .operations-layout {
        grid-template-columns: 350px 1fr;
        gap: 1rem;
    }
    
    .account-summary {
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .account-summary {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }
    
    .operations-layout {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .trading-form-panel {
        order: 1;
    }
    
    .stock-info-panel {
        order: 2;
    }
    
    .quick-buttons-grid {
        grid-template-columns: 1fr;
    }
    
    .quantity-buttons {
        flex-direction: column;
    }
    
    .quantity-buttons .btn {
        flex: none;
    }
    
    .radio-group {
        flex-direction: column;
        gap: 0.75rem;
    }
}

@media (max-width: 480px) {
    .page-header h2 {
        font-size: 1.25rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .account-summary {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .trade-type-switcher {
        flex-direction: column;
    }
    
    .stock-stats {
        grid-template-columns: 1fr;
    }
}

/* 滚动条样式 */
.trading-results::-webkit-scrollbar,
.stock-selector-list::-webkit-scrollbar {
    width: 6px;
}

.trading-results::-webkit-scrollbar-track,
.stock-selector-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.trading-results::-webkit-scrollbar-thumb,
.stock-selector-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.trading-results::-webkit-scrollbar-thumb:hover,
.stock-selector-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
.result-item {
    animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
