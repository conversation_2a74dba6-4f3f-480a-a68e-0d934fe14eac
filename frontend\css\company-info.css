/* 公司基本信息查询页面样式 */
.page-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1.5rem 2rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-header h2 {
    color: #2c3e50;
    font-size: 1.75rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-header h2 i {
    color: #4a90e2;
}

.page-actions {
    display: flex;
    gap: 0.75rem;
}

/* 搜索区域 */
.search-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.search-container {
    position: relative;
    margin-bottom: 1.5rem;
}

.search-box {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-box .form-control {
    flex: 1;
    padding-left: 2.5rem;
    font-size: 1rem;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    z-index: 1;
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: none;
    max-height: 300px;
    overflow-y: auto;
    margin-top: 0.5rem;
}

.suggestion-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: background 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.suggestion-item:hover {
    background: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.quick-search {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.quick-label {
    color: #666;
    font-weight: 500;
    white-space: nowrap;
}

.quick-company-list {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.quick-company-btn {
    padding: 0.75rem 1rem;
    border: 1px solid #e9ecef;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    min-width: 100px;
}

.quick-company-btn:hover {
    border-color: #4a90e2;
    background: #f0f7ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.2);
}

.company-code {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

.company-name {
    font-size: 0.8rem;
    color: #666;
}

/* 信息展示区域 */
.info-display-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    min-height: 500px;
}

/* 空状态和错误状态 */
.no-company-selected,
.query-failed {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 500px;
    padding: 2rem;
}

.empty-state,
.error-state {
    text-align: center;
    color: #666;
}

.empty-state i,
.error-state i {
    font-size: 4rem;
    color: #ccc;
    margin-bottom: 1.5rem;
}

.error-state i {
    color: #ffc107;
}

.empty-state h3,
.error-state h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.empty-state p,
.error-state p {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

/* 公司信息展示 */
.company-info-display {
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.company-header {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.company-main-info {
    flex: 1;
}

.company-full-name {
    font-size: 1.75rem;
    margin: 0 0 0.75rem 0;
    font-weight: 600;
}

.company-meta {
    display: flex;
    gap: 1.5rem;
    align-items: center;
    flex-wrap: wrap;
}

.company-meta span {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.company-actions {
    display: flex;
    gap: 1rem;
}

.company-actions .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
}

.company-actions .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

/* 信息标签页 */
.info-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.info-tab {
    flex: 1;
    padding: 1.25rem 1.5rem;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
    color: #666;
}

.info-tab:hover {
    background: #e9ecef;
    color: #495057;
}

.info-tab.active {
    background: white;
    color: #4a90e2;
    border-bottom: 3px solid #4a90e2;
}

.tab-content-container {
    padding: 2rem;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 基本信息网格 */
.basic-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.info-card {
    background: #f8f9fa;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.info-card:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.info-card-header {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    padding: 1rem 1.5rem;
}

.info-card-header h4 {
    margin: 0;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-card-body {
    padding: 1.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    color: #666;
    font-weight: 500;
    flex-shrink: 0;
    min-width: 120px;
}

.info-value {
    color: #2c3e50;
    font-weight: 600;
    text-align: right;
    word-break: break-all;
}

/* 财务数据 */
.financial-data-container {
    display: grid;
    gap: 2rem;
}

.financial-summary {
    margin-bottom: 2rem;
}

.financial-summary h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.financial-table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.financial-table {
    margin: 0;
    font-size: 0.9rem;
}

.financial-table th {
    background: #f8f9fa;
    font-weight: 600;
    text-align: center;
    padding: 1rem 0.75rem;
    border-bottom: 2px solid #e9ecef;
}

.financial-table td {
    padding: 0.75rem;
    text-align: right;
    font-family: 'Courier New', monospace;
    border-bottom: 1px solid #f8f9fa;
}

.financial-table td:first-child {
    text-align: left;
    font-family: inherit;
    font-weight: 500;
}

.financial-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin: 2rem 0;
}

.chart-container {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
}

.chart-container h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.chart-container canvas {
    max-height: 300px;
}

/* 财务比率 */
.financial-ratios {
    margin-top: 2rem;
}

.financial-ratios h4 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ratios-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.ratio-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.ratio-card:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.ratio-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
    text-align: center;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #4a90e2;
}

.ratio-items {
    display: grid;
    gap: 0.75rem;
}

.ratio-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.ratio-label {
    color: #666;
    font-weight: 500;
}

.ratio-value {
    color: #2c3e50;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

/* 主营业务 */
.business-info-container {
    display: grid;
    gap: 2rem;
}

.business-overview .info-card-body {
    padding: 2rem;
}

.business-description {
    line-height: 1.8;
    color: #495057;
    font-size: 1rem;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.product-item {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    text-align: center;
}

.product-item:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.product-icon {
    font-size: 2.5rem;
    color: #4a90e2;
    margin-bottom: 1rem;
}

.product-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.product-description {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
}

.market-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.market-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.market-label {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.market-value {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.1rem;
}

.segments-chart-container {
    margin-bottom: 1.5rem;
}

.segments-chart-container canvas {
    max-height: 400px;
}

.segments-legend {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    flex-shrink: 0;
}

.legend-label {
    font-size: 0.9rem;
    color: #495057;
    font-weight: 500;
}

.legend-value {
    font-size: 0.8rem;
    color: #666;
    margin-left: auto;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .basic-info-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
    
    .financial-charts {
        grid-template-columns: 1fr;
    }
    
    .ratios-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .search-box {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .quick-search {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .quick-company-list {
        width: 100%;
        justify-content: space-between;
    }
    
    .quick-company-btn {
        flex: 1;
        min-width: auto;
    }
    
    .company-header {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }
    
    .company-meta {
        justify-content: center;
    }
    
    .info-tabs {
        flex-direction: column;
    }
    
    .tab-content-container {
        padding: 1rem;
    }
    
    .basic-info-grid {
        grid-template-columns: 1fr;
    }
    
    .financial-table-container {
        font-size: 0.8rem;
    }
    
    .products-grid {
        grid-template-columns: 1fr;
    }
    
    .market-info-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .page-header h2 {
        font-size: 1.25rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .company-full-name {
        font-size: 1.25rem;
    }
    
    .company-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .info-tab {
        padding: 1rem;
        font-size: 0.9rem;
    }
    
    .market-info-grid {
        grid-template-columns: 1fr;
    }
    
    .segments-legend {
        grid-template-columns: 1fr;
    }
}

/* 滚动条样式 */
.financial-table-container::-webkit-scrollbar,
.search-suggestions::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.financial-table-container::-webkit-scrollbar-track,
.search-suggestions::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.financial-table-container::-webkit-scrollbar-thumb,
.search-suggestions::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.financial-table-container::-webkit-scrollbar-thumb:hover,
.search-suggestions::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
