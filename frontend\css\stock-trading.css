/* 股票实时交易模拟页面样式 */
.page-header {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(6px);
    border-radius: 8px;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-header h2 {
    color: #8e8e93;
    font-size: 1.3rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
}

.page-header h2 i {
    color: #c8a2c8;
}

.market-status {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    font-weight: 400;
    font-size: 11px;
    color: #a8a8a8;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #f8a5c2;
    animation: pulse 2s infinite;
}

.status-dot.active {
    background: #c8d6e5;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(200, 214, 229, 0.5);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(200, 214, 229, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(200, 214, 229, 0);
    }
}

.market-time {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    color: #a8a8a8;
    font-family: 'Courier New', monospace;
    font-size: 11px;
}

/* 搜索区域 */
.search-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.search-container {
    position: relative;
    margin-bottom: 1rem;
}

.search-box {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-box .form-control {
    flex: 1;
    padding-left: 2.5rem;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    z-index: 1;
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: none;
    max-height: 300px;
    overflow-y: auto;
}

.suggestion-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: background 0.3s ease;
}

.suggestion-item:hover {
    background: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.quick-stocks {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.quick-label {
    color: #666;
    font-weight: 500;
    white-space: nowrap;
}

.quick-stock-list {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.quick-stock-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #e9ecef;
    background: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    color: #495057;
}

.quick-stock-btn:hover {
    border-color: #4a90e2;
    background: #f0f7ff;
    color: #4a90e2;
}

/* 交易布局 */
.trading-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

/* 面板通用样式 */
.market-panel,
.chart-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.panel-header {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    margin: 0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.panel-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.panel-controls .form-select {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.375rem 0.75rem;
    font-size: 0.85rem;
    min-width: 100px;
}

.refresh-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.refresh-controls .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.375rem 0.75rem;
    font-size: 0.85rem;
}

/* 行情表格 */
.market-table-container {
    max-height: 500px;
    overflow-y: auto;
}

.market-table {
    margin: 0;
    font-size: 0.9rem;
}

.market-table th {
    background: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 10;
    font-size: 0.85rem;
    padding: 0.75rem 0.5rem;
}

.market-table td {
    padding: 0.5rem;
    border-bottom: 1px solid #f8f9fa;
    font-family: 'Courier New', monospace;
}

.market-table tbody tr {
    cursor: pointer;
    transition: background 0.3s ease;
}

.market-table tbody tr:hover {
    background: #f8f9fa;
}

.market-table tbody tr.selected {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
}

/* 价格颜色 */
.price-up {
    color: #dc3545 !important;
}

.price-down {
    color: #28a745 !important;
}

.price-equal {
    color: #6c757d !important;
}

/* K线图区域 */
.chart-section {
    border-bottom: 1px solid #e9ecef;
}

.chart-header {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
}

.stock-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stock-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1rem;
}

.stock-price {
    font-family: 'Courier New', monospace;
    font-size: 1.25rem;
    font-weight: 700;
}

.stock-change {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.chart-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.chart-type-tabs {
    display: flex;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
}

.chart-tab {
    padding: 0.5rem 1rem;
    border: none;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.85rem;
    border-right: 1px solid #e9ecef;
}

.chart-tab:last-child {
    border-right: none;
}

.chart-tab:hover {
    background: #f8f9fa;
}

.chart-tab.active {
    background: #4a90e2;
    color: white;
}

.chart-tools {
    display: flex;
    gap: 0.5rem;
}

.chart-tools .btn {
    padding: 0.5rem;
    font-size: 0.85rem;
}

.chart-container {
    padding: 1rem;
    height: 300px;
    position: relative;
}

/* 买五卖五盘口 */
.orderbook-section {
    padding: 1rem 1.5rem;
}

.orderbook-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.orderbook-header h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.orderbook-time {
    font-family: 'Courier New', monospace;
    color: #666;
    font-size: 0.85rem;
}

.orderbook-container {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 1rem;
}

.orderbook-side {
    display: flex;
    flex-direction: column;
}

.side-header {
    text-align: center;
    font-weight: 600;
    padding: 0.5rem;
    border-radius: 6px;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.sell-side .side-header {
    background: #f8d7da;
    color: #721c24;
}

.buy-side .side-header {
    background: #d4edda;
    color: #155724;
}

.orderbook-levels {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.orderbook-level {
    display: flex;
    justify-content: space-between;
    padding: 0.375rem 0.75rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    transition: background 0.3s ease;
}

.sell-side .orderbook-level {
    background: linear-gradient(90deg, transparent, #f8d7da 100%);
    color: #721c24;
}

.buy-side .orderbook-level {
    background: linear-gradient(90deg, transparent, #d4edda 100%);
    color: #155724;
}

.orderbook-level:hover {
    opacity: 0.8;
}

.orderbook-middle {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1rem 0;
    border-left: 1px solid #e9ecef;
    border-right: 1px solid #e9ecef;
}

.current-price {
    text-align: center;
    margin-bottom: 0.5rem;
}

.price-value {
    font-family: 'Courier New', monospace;
    font-size: 1.25rem;
    font-weight: 700;
}

.price-change {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    margin-top: 0.25rem;
}

.spread-info {
    font-size: 0.8rem;
    color: #666;
}

/* 交易信息面板 */
.trading-info-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.info-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.info-tab {
    flex: 1;
    padding: 1rem;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

.info-tab:hover {
    background: #e9ecef;
    color: #495057;
}

.info-tab.active {
    background: white;
    color: #4a90e2;
    border-bottom: 2px solid #4a90e2;
}

.info-content {
    padding: 1.5rem;
    min-height: 300px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 成交明细表格 */
.trades-table-container {
    max-height: 250px;
    overflow-y: auto;
}

.trades-table {
    margin: 0;
    font-size: 0.85rem;
}

.trades-table th {
    background: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 10;
    padding: 0.75rem 0.5rem;
}

.trades-table td {
    padding: 0.5rem;
    font-family: 'Courier New', monospace;
}

/* 资讯列表 */
.news-list {
    max-height: 250px;
    overflow-y: auto;
}

.news-item {
    padding: 1rem;
    border-bottom: 1px solid #f8f9fa;
    cursor: pointer;
    transition: background 0.3s ease;
}

.news-item:hover {
    background: #f8f9fa;
}

.news-item:last-child {
    border-bottom: none;
}

.news-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.news-summary {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

.news-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #999;
}

/* 技术分析 */
.analysis-indicators {
    display: grid;
    gap: 1.5rem;
}

.indicator-group h5 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.indicators-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.indicator-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #4a90e2;
}

.indicator-name {
    font-weight: 500;
    color: #495057;
}

.indicator-value {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #2c3e50;
}

/* 持仓信息 */
.portfolio-summary {
    margin-bottom: 1.5rem;
}

.portfolio-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.stat-label {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.stat-value {
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.stat-value.profit {
    color: #dc3545;
}

.stat-value.loss {
    color: #28a745;
}

.portfolio-holdings {
    max-height: 200px;
    overflow-y: auto;
}

.portfolio-holdings .table {
    margin: 0;
    font-size: 0.85rem;
}

.portfolio-holdings .table th {
    background: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 10;
}

.portfolio-holdings .table td {
    font-family: 'Courier New', monospace;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .trading-layout {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .chart-panel {
        order: 1;
    }
    
    .market-panel {
        order: 2;
    }
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .market-status {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .search-box {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .quick-stocks {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .chart-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .orderbook-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .orderbook-middle {
        border: 1px solid #e9ecef;
        border-radius: 6px;
    }
    
    .info-tabs {
        flex-wrap: wrap;
    }
    
    .info-tab {
        flex: 1 1 50%;
        min-width: 120px;
    }
    
    .portfolio-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .page-header h2 {
        font-size: 1.25rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .panel-controls {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .chart-type-tabs {
        flex-wrap: wrap;
    }
    
    .chart-tab {
        flex: 1;
        min-width: 60px;
    }
    
    .indicators-grid {
        grid-template-columns: 1fr;
    }
    
    .portfolio-stats {
        grid-template-columns: 1fr;
    }
}

/* 滚动条样式 */
.market-table-container::-webkit-scrollbar,
.trades-table-container::-webkit-scrollbar,
.news-list::-webkit-scrollbar,
.portfolio-holdings::-webkit-scrollbar {
    width: 6px;
}

.market-table-container::-webkit-scrollbar-track,
.trades-table-container::-webkit-scrollbar-track,
.news-list::-webkit-scrollbar-track,
.portfolio-holdings::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.market-table-container::-webkit-scrollbar-thumb,
.trades-table-container::-webkit-scrollbar-thumb,
.news-list::-webkit-scrollbar-thumb,
.portfolio-holdings::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.market-table-container::-webkit-scrollbar-thumb:hover,
.trades-table-container::-webkit-scrollbar-thumb:hover,
.news-list::-webkit-scrollbar-thumb:hover,
.portfolio-holdings::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
