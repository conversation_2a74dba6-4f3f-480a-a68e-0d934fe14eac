<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能管理 - 金融软件项目管理系统</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/function-management.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                <h1>金融软件项目管理系统</h1>
            </div>
            <nav class="nav">
                <a href="index.html" class="btn btn-secondary">
                    <i class="fas fa-home"></i> 返回首页
                </a>
                <div class="user-info">
                    <span>欢迎，管理员</span>
                    <i class="fas fa-user-circle"></i>
                </div>
            </nav>
        </header>

        <main class="main">
            <div class="page-header">
                <h2><i class="fas fa-cogs"></i> 功能管理</h2>
                <p>管理系统功能模块和权限配置</p>
            </div>

            <div class="content-layout">
                <!-- 左侧功能列表 -->
                <div class="function-list-panel">
                    <div class="panel-header">
                        <h3><i class="fas fa-list"></i> 功能列表</h3>
                        <div class="panel-actions">
                            <button class="btn btn-secondary" onclick="expandAll()">
                                <i class="fas fa-expand-arrows-alt"></i> 展开全部
                            </button>
                            <button class="btn btn-secondary" onclick="collapseAll()">
                                <i class="fas fa-compress-arrows-alt"></i> 收起全部
                            </button>
                        </div>
                    </div>
                    
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="functionSearch" placeholder="搜索功能..." class="form-control">
                    </div>

                    <div class="function-tree" id="functionTree">
                        <!-- 功能树将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 右侧功能配置 -->
                <div class="function-config-panel">
                    <div class="panel-header">
                        <h3><i class="fas fa-wrench"></i> 功能配置</h3>
                        <div class="panel-actions">
                            <button class="btn btn-primary" onclick="showAddFunctionModal()">
                                <i class="fas fa-plus"></i> 新增功能
                            </button>
                            <button class="btn btn-success" onclick="saveAllChanges()">
                                <i class="fas fa-save"></i> 保存所有更改
                            </button>
                        </div>
                    </div>

                    <div class="config-content" id="configContent">
                        <div class="welcome-message">
                            <i class="fas fa-hand-point-left"></i>
                            <h4>选择功能进行配置</h4>
                            <p>请从左侧功能列表中选择一个功能来查看和编辑其配置信息</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 批量操作面板 -->
            <div class="batch-operations">
                <div class="batch-header">
                    <h3><i class="fas fa-tasks"></i> 批量操作</h3>
                    <div class="selected-count">
                        已选择 <span id="selectedCount">0</span> 个功能
                    </div>
                </div>
                <div class="batch-actions">
                    <button class="btn btn-success" onclick="batchEnable()" id="batchEnableBtn" disabled>
                        <i class="fas fa-check"></i> 批量启用
                    </button>
                    <button class="btn btn-danger" onclick="batchDisable()" id="batchDisableBtn" disabled>
                        <i class="fas fa-ban"></i> 批量禁用
                    </button>
                    <button class="btn btn-secondary" onclick="batchExport()" id="batchExportBtn" disabled>
                        <i class="fas fa-download"></i> 导出选中
                    </button>
                    <button class="btn btn-secondary" onclick="clearSelection()">
                        <i class="fas fa-times"></i> 清除选择
                    </button>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增功能模态框 -->
    <div class="modal" id="addFunctionModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">新增功能</h3>
                <button class="modal-close" onclick="hideAddFunctionModal()">&times;</button>
            </div>
            <form id="addFunctionForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label">父级功能</label>
                        <select class="form-control form-select" id="parentFunction" name="parentFunction">
                            <option value="">选择父级功能（可选）</option>
                            <!-- 选项将动态生成 -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">功能名称 <span class="required">*</span></label>
                        <input type="text" class="form-control" id="functionName" name="functionName" placeholder="请输入功能名称" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">功能标识 <span class="required">*</span></label>
                        <input type="text" class="form-control" id="functionCode" name="functionCode" placeholder="例如：stock_buy" required>
                        <small class="form-text">功能标识用于系统内部识别，建议使用英文和下划线</small>
                    </div>
                    <div class="form-group">
                        <label class="form-label">功能URL</label>
                        <input type="text" class="form-control" id="functionUrl" name="functionUrl" placeholder="例如：/stock/buy">
                    </div>
                    <div class="form-group">
                        <label class="form-label">功能描述</label>
                        <textarea class="form-control" id="functionDescription" name="functionDescription" rows="3" placeholder="请输入功能描述"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">图标</label>
                        <div class="icon-selector">
                            <input type="text" class="form-control" id="functionIcon" name="functionIcon" placeholder="例如：fas fa-chart-line" readonly>
                            <button type="button" class="btn btn-secondary" onclick="showIconPicker()">
                                <i class="fas fa-icons"></i> 选择图标
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">排序</label>
                        <input type="number" class="form-control" id="functionSort" name="functionSort" value="0" min="0">
                    </div>
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="functionEnabled" name="functionEnabled" checked>
                            <label class="form-check-label" for="functionEnabled">启用此功能</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="hideAddFunctionModal()">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 图标选择器模态框 -->
    <div class="modal" id="iconPickerModal">
        <div class="modal-content icon-picker-content">
            <div class="modal-header">
                <h3 class="modal-title">选择图标</h3>
                <button class="modal-close" onclick="hideIconPicker()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="icon-search">
                    <input type="text" class="form-control" id="iconSearch" placeholder="搜索图标...">
                </div>
                <div class="icon-grid" id="iconGrid">
                    <!-- 图标网格将动态生成 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideIconPicker()">取消</button>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script src="js/function-management.js"></script>
</body>
</html>
