<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限分配管理 - 金融软件项目管理系统</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/permission-assignment.css">
    <link rel="stylesheet" href="css/macaron-theme.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body data-page="permission-assignment">
    <div class="container">
        <header class="header">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                <h1>金融软件项目管理系统</h1>
            </div>
            <nav class="nav">
                <a href="index.html" class="btn btn-secondary">
                    <i class="fas fa-home"></i> 返回首页
                </a>
                <div class="user-info">
                    <span>欢迎，管理员</span>
                    <i class="fas fa-user-circle"></i>
                </div>
            </nav>
        </header>

        <main class="main">
            <div class="page-header">
                <h2><i class="fas fa-key"></i> 权限分配管理</h2>
                <p>配置角色的具体功能权限</p>
            </div>

            <div class="permission-layout">
                <!-- 角色选择区域 -->
                <div class="role-selection-panel">
                    <div class="panel-header">
                        <h3><i class="fas fa-user-shield"></i> 角色选择</h3>
                        <div class="panel-actions">
                            <button class="btn btn-secondary" onclick="refreshRoles()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                            <button class="btn btn-secondary" onclick="exportPermissions()">
                                <i class="fas fa-download"></i> 导出配置
                            </button>
                        </div>
                    </div>
                    
                    <div class="role-selection-content">
                        <div class="role-search">
                            <i class="fas fa-search"></i>
                            <input type="text" id="roleSearch" placeholder="搜索角色..." class="form-control">
                        </div>
                        
                        <div class="role-list" id="roleList">
                            <!-- 角色列表将动态生成 -->
                        </div>
                        
                        <div class="role-stats">
                            <h4><i class="fas fa-chart-pie"></i> 权限统计</h4>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-number" id="totalPermissions">0</div>
                                    <div class="stat-label">总权限数</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number" id="assignedPermissions">0</div>
                                    <div class="stat-label">已分配</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number" id="unassignedPermissions">0</div>
                                    <div class="stat-label">未分配</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 权限配置区域 -->
                <div class="permission-config-panel">
                    <div class="panel-header">
                        <h3><i class="fas fa-cogs"></i> 功能权限列表</h3>
                        <div class="panel-actions">
                            <button class="btn btn-success" onclick="selectAll()" id="selectAllBtn">
                                <i class="fas fa-check-square"></i> 全选
                            </button>
                            <button class="btn btn-secondary" onclick="unselectAll()" id="unselectAllBtn">
                                <i class="fas fa-square"></i> 取消全选
                            </button>
                            <button class="btn btn-primary" onclick="savePermissions()" id="savePermissionsBtn" disabled>
                                <i class="fas fa-save"></i> 保存权限
                            </button>
                        </div>
                    </div>

                    <div class="permission-content" id="permissionContent">
                        <div class="welcome-message">
                            <i class="fas fa-hand-point-left"></i>
                            <h4>选择角色配置权限</h4>
                            <p>请从左侧角色列表中选择一个角色来配置其功能权限</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 权限对比面板 -->
            <div class="permission-compare-panel">
                <div class="panel-header">
                    <h3><i class="fas fa-balance-scale"></i> 权限对比</h3>
                    <div class="panel-actions">
                        <select class="form-control form-select" id="compareRole1">
                            <option value="">选择角色1</option>
                        </select>
                        <span style="color: white; margin: 0 1rem;">VS</span>
                        <select class="form-control form-select" id="compareRole2">
                            <option value="">选择角色2</option>
                        </select>
                        <button class="btn btn-secondary" onclick="comparePermissions()">
                            <i class="fas fa-search"></i> 对比
                        </button>
                    </div>
                </div>
                <div class="compare-content" id="compareContent">
                    <div class="compare-placeholder">
                        <i class="fas fa-balance-scale"></i>
                        <p>选择两个角色进行权限对比</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 权限详情模态框 -->
    <div class="modal" id="permissionDetailModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">权限详情</h3>
                <button class="modal-close" onclick="hidePermissionDetail()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="permission-detail-content" id="permissionDetailContent">
                    <!-- 权限详情将动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hidePermissionDetail()">关闭</button>
            </div>
        </div>
    </div>

    <!-- 批量操作模态框 -->
    <div class="modal" id="batchOperationModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">批量权限操作</h3>
                <button class="modal-close" onclick="hideBatchOperation()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="batch-operation-content">
                    <div class="form-group">
                        <label class="form-label">操作类型</label>
                        <select class="form-control form-select" id="batchOperationType">
                            <option value="assign">批量分配权限</option>
                            <option value="remove">批量移除权限</option>
                            <option value="copy">复制权限配置</option>
                        </select>
                    </div>
                    <div class="form-group" id="sourceRoleGroup" style="display: none;">
                        <label class="form-label">源角色</label>
                        <select class="form-control form-select" id="sourceRole">
                            <option value="">选择源角色</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">目标角色</label>
                        <div class="target-roles" id="targetRoles">
                            <!-- 目标角色复选框将动态生成 -->
                        </div>
                    </div>
                    <div class="form-group" id="permissionSelectionGroup">
                        <label class="form-label">选择权限</label>
                        <div class="permission-tree-mini" id="permissionTreeMini">
                            <!-- 权限树将动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideBatchOperation()">取消</button>
                <button type="button" class="btn btn-primary" onclick="executeBatchOperation()">
                    <i class="fas fa-check"></i> 执行操作
                </button>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script src="js/permission-assignment.js"></script>
</body>
</html>
