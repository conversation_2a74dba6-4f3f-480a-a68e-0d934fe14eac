// 股票买卖操作页面JavaScript

// 模拟股票数据
let stocksData = [
    {
        code: '000001',
        name: '平安银行',
        market: 'sz',
        currentPrice: 12.85,
        openPrice: 12.60,
        highPrice: 13.20,
        lowPrice: 12.45,
        prevClose: 12.70,
        volume: 125680000,
        turnover: 1612345000,
        change: 0.15,
        changePercent: 1.18
    },
    {
        code: '000002',
        name: '万科A',
        market: 'sz',
        currentPrice: 8.95,
        openPrice: 9.10,
        highPrice: 9.25,
        lowPrice: 8.88,
        prevClose: 9.05,
        volume: 89560000,
        turnover: 805234000,
        change: -0.10,
        changePercent: -1.10
    },
    {
        code: '600036',
        name: '招商银行',
        market: 'sh',
        currentPrice: 35.68,
        openPrice: 35.20,
        highPrice: 36.15,
        lowPrice: 35.05,
        prevClose: 35.45,
        volume: 45230000,
        turnover: 1612890000,
        change: 0.23,
        changePercent: 0.65
    },
    {
        code: '600519',
        name: '贵州茅台',
        market: 'sh',
        currentPrice: 1685.50,
        openPrice: 1678.00,
        highPrice: 1698.80,
        lowPrice: 1672.30,
        prevClose: 1680.20,
        volume: 1250000,
        turnover: 2108750000,
        change: 5.30,
        changePercent: 0.32
    },
    {
        code: '000858',
        name: '五粮液',
        market: 'sz',
        currentPrice: 128.45,
        openPrice: 126.80,
        highPrice: 130.20,
        lowPrice: 125.90,
        prevClose: 127.30,
        volume: 8950000,
        turnover: 1148230000,
        change: 1.15,
        changePercent: 0.90
    }
];

// 模拟持仓数据
let portfolioData = [
    {
        code: '000001',
        name: '平安银行',
        quantity: 10000,
        costPrice: 12.20,
        currentPrice: 12.85
    },
    {
        code: '600036',
        name: '招商银行',
        quantity: 5000,
        costPrice: 34.80,
        currentPrice: 35.68
    },
    {
        code: '000858',
        name: '五粮液',
        quantity: 1000,
        costPrice: 130.50,
        currentPrice: 128.45
    }
];

// 全局变量
let currentTradeType = 'buy';
let selectedStock = null;
let availableFunds = 500000; // 可用资金
let tradingResults = [];
let currentSelectorType = 'buy';

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    bindEvents();
    loadHoldingStocks();
    updateAccountInfo();
});

// 初始化页面
function initializePage() {
    // 初始化搜索
    const stockSearchInput = document.getElementById('stockSearchInput');
    stockSearchInput.addEventListener('input', Utils.debounce(handleStockSearch, 300));
    
    // 初始化表单验证
    bindFormEvents();
    
    // 初始化股票选择器搜索
    const selectorSearch = document.getElementById('selectorSearch');
    selectorSearch.addEventListener('input', Utils.debounce(handleSelectorSearch, 300));
}

// 绑定事件
function bindEvents() {
    // 买入表单事件
    const buyForm = document.getElementById('buyOrderForm');
    buyForm.addEventListener('submit', handleBuyOrder);
    
    // 卖出表单事件
    const sellForm = document.getElementById('sellOrderForm');
    sellForm.addEventListener('submit', handleSellOrder);
    
    // 输入框变化事件
    document.getElementById('buyQuantity').addEventListener('input', updateBuyEstimation);
    document.getElementById('buyLimitPrice').addEventListener('input', updateBuyEstimation);
    document.getElementById('sellQuantity').addEventListener('input', updateSellEstimation);
    document.getElementById('sellLimitPrice').addEventListener('input', updateSellEstimation);
    document.getElementById('sellStockCode').addEventListener('change', handleSellStockChange);
}

// 绑定表单事件
function bindFormEvents() {
    // 买入股票代码输入
    const buyStockCode = document.getElementById('buyStockCode');
    buyStockCode.addEventListener('input', function() {
        const stockCode = this.value.toUpperCase();
        const stock = stocksData.find(s => s.code === stockCode);
        if (stock) {
            selectStockForTrading(stockCode);
        }
        validateBuyForm();
    });
    
    // 买入数量输入
    const buyQuantity = document.getElementById('buyQuantity');
    buyQuantity.addEventListener('input', validateBuyForm);
    
    // 卖出数量输入
    const sellQuantity = document.getElementById('sellQuantity');
    sellQuantity.addEventListener('input', validateSellForm);
}

// 处理股票搜索
function handleStockSearch() {
    const searchTerm = document.getElementById('stockSearchInput').value.toLowerCase();
    const suggestions = document.getElementById('stockSuggestions');
    
    if (!searchTerm) {
        suggestions.style.display = 'none';
        return;
    }
    
    const filteredStocks = stocksData.filter(stock => 
        stock.code.toLowerCase().includes(searchTerm) ||
        stock.name.toLowerCase().includes(searchTerm)
    );
    
    if (filteredStocks.length > 0) {
        suggestions.innerHTML = filteredStocks.map(stock => {
            const changeClass = stock.change >= 0 ? 'price-up' : 'price-down';
            const changeSymbol = stock.change >= 0 ? '+' : '';
            
            return `
                <div class="suggestion-item" onclick="selectStockFromSearch('${stock.code}')">
                    <div>
                        <strong>${stock.code}</strong> ${stock.name}
                    </div>
                    <div class="${changeClass}">
                        ${stock.currentPrice.toFixed(2)}
                        <small>(${changeSymbol}${stock.changePercent.toFixed(2)}%)</small>
                    </div>
                </div>
            `;
        }).join('');
        suggestions.style.display = 'block';
    } else {
        suggestions.style.display = 'none';
    }
}

// 从搜索结果选择股票
function selectStockFromSearch(stockCode) {
    document.getElementById('stockSearchInput').value = '';
    document.getElementById('stockSuggestions').style.display = 'none';
    selectStockForTrading(stockCode);
}

// 搜索股票信息
function searchStockInfo() {
    const searchTerm = document.getElementById('stockSearchInput').value;
    if (searchTerm) {
        const stock = stocksData.find(s => 
            s.code === searchTerm.toUpperCase() || s.name === searchTerm
        );
        if (stock) {
            selectStockForTrading(stock.code);
        } else {
            Toast.show('未找到相关股票', 'warning');
        }
    }
}

// 选择股票进行交易
function selectStockForTrading(stockCode) {
    const stock = stocksData.find(s => s.code === stockCode);
    if (!stock) return;
    
    selectedStock = stock;
    updateStockInfo(stock);
    
    // 更新买入表单的股票代码
    if (currentTradeType === 'buy') {
        document.getElementById('buyStockCode').value = stockCode;
        validateBuyForm();
    }
}

// 更新股票信息显示
function updateStockInfo(stock) {
    const stockInfoContainer = document.getElementById('currentStockInfo');
    const changeClass = stock.change >= 0 ? 'price-up' : 'price-down';
    const changeSymbol = stock.change >= 0 ? '+' : '';
    
    stockInfoContainer.innerHTML = `
        <div class="stock-detail-info">
            <div class="stock-header">
                <div class="stock-title">
                    <div class="stock-code">${stock.code}</div>
                    <div class="stock-name">${stock.name}</div>
                </div>
                <div class="stock-price-info">
                    <div class="current-price ${changeClass}">${stock.currentPrice.toFixed(2)}</div>
                    <div class="price-change ${changeClass}">
                        ${changeSymbol}${stock.change.toFixed(2)} (${changeSymbol}${stock.changePercent.toFixed(2)}%)
                    </div>
                </div>
            </div>
            <div class="stock-stats">
                <div class="stat-item">
                    <span class="stat-label">开盘价</span>
                    <span class="stat-value">${stock.openPrice.toFixed(2)}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">昨收价</span>
                    <span class="stat-value">${stock.prevClose.toFixed(2)}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">最高价</span>
                    <span class="stat-value price-up">${stock.highPrice.toFixed(2)}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">最低价</span>
                    <span class="stat-value price-down">${stock.lowPrice.toFixed(2)}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">成交量</span>
                    <span class="stat-value">${Utils.formatNumber(stock.volume / 10000, 0)}万</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">成交额</span>
                    <span class="stat-value">${Utils.formatNumber(stock.turnover / 100000000, 2)}亿</span>
                </div>
            </div>
        </div>
    `;
}

// 切换交易类型
function switchTradeType(type) {
    currentTradeType = type;

    // 更新按钮状态
    document.querySelectorAll('.trade-type-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`.${type}-btn`).classList.add('active');

    // 切换表单显示
    document.querySelectorAll('.trade-form').forEach(form => {
        form.classList.remove('active');
    });
    document.getElementById(`${type}Form`).classList.add('active');

    // 重置表单
    resetForm();
}

// 重置表单
function resetForm() {
    if (currentTradeType === 'buy') {
        document.getElementById('buyOrderForm').reset();
        document.getElementById('buySubmitBtn').disabled = true;
        updateBuyEstimation();
    } else {
        document.getElementById('sellOrderForm').reset();
        document.getElementById('sellSubmitBtn').disabled = true;
        updateSellEstimation();
    }
}

// 刷新股票信息
function refreshStockInfo() {
    // 模拟刷新股票价格
    stocksData.forEach(stock => {
        const variation = (Math.random() - 0.5) * 0.02; // ±1%的随机波动
        const newPrice = Math.max(0.01, stock.currentPrice * (1 + variation));

        stock.currentPrice = parseFloat(newPrice.toFixed(2));
        stock.change = stock.currentPrice - stock.prevClose;
        stock.changePercent = (stock.change / stock.prevClose) * 100;

        // 更新最高最低价
        stock.highPrice = Math.max(stock.highPrice, stock.currentPrice);
        stock.lowPrice = Math.min(stock.lowPrice, stock.currentPrice);
    });

    // 更新持仓数据
    portfolioData.forEach(holding => {
        const stock = stocksData.find(s => s.code === holding.code);
        if (stock) {
            holding.currentPrice = stock.currentPrice;
        }
    });

    // 如果有选中的股票，更新显示
    if (selectedStock) {
        const updatedStock = stocksData.find(s => s.code === selectedStock.code);
        if (updatedStock) {
            selectedStock = updatedStock;
            updateStockInfo(updatedStock);
        }
    }

    // 更新快速按钮的价格
    updateQuickStockButtons();

    // 重新加载持仓股票
    loadHoldingStocks();

    Toast.show('股票信息已刷新', 'success');
}

// 更新快速股票按钮
function updateQuickStockButtons() {
    const quickButtons = document.querySelectorAll('.quick-stock-btn');
    quickButtons.forEach(button => {
        const stockCode = button.getAttribute('onclick').match(/'([^']+)'/)[1];
        const stock = stocksData.find(s => s.code === stockCode);
        if (stock) {
            const priceElement = button.querySelector('.stock-price');
            const changeClass = stock.change >= 0 ? 'price-up' : 'price-down';
            priceElement.textContent = stock.currentPrice.toFixed(2);
            priceElement.className = `stock-price ${changeClass}`;
        }
    });
}

// 设置买入数量
function setQuantity(type, quantity) {
    if (type === 'buy') {
        document.getElementById('buyQuantity').value = quantity;
        updateBuyEstimation();
        validateBuyForm();
    }
}

// 计算最大可买数量
function calculateMaxBuyQuantity() {
    if (!selectedStock) {
        Toast.show('请先选择股票', 'warning');
        return;
    }

    const price = getCurrentPrice('buy');
    if (price <= 0) return;

    const maxQuantity = Math.floor(availableFunds / price / 100) * 100; // 向下取整到100的倍数
    document.getElementById('buyQuantity').value = maxQuantity;
    updateBuyEstimation();
    validateBuyForm();
}

// 设置卖出数量（按比例）
function setSellQuantity(ratio) {
    const sellStockCode = document.getElementById('sellStockCode').value;
    if (!sellStockCode) {
        Toast.show('请先选择要卖出的股票', 'warning');
        return;
    }

    const holding = portfolioData.find(h => h.code === sellStockCode);
    if (holding) {
        const quantity = Math.floor(holding.quantity * ratio / 100) * 100; // 向下取整到100的倍数
        document.getElementById('sellQuantity').value = quantity;
        updateSellEstimation();
        validateSellForm();
    }
}

// 处理价格类型变化
function handlePriceTypeChange(type) {
    const limitPriceInput = document.getElementById(`${type}LimitPriceInput`);
    const priceType = document.querySelector(`input[name="${type}PriceType"]:checked`).value;

    if (priceType === 'limit') {
        limitPriceInput.style.display = 'block';
        // 设置默认限价为当前价格
        if (selectedStock) {
            document.getElementById(`${type}LimitPrice`).value = selectedStock.currentPrice.toFixed(2);
        }
    } else {
        limitPriceInput.style.display = 'none';
    }

    // 更新估算
    if (type === 'buy') {
        updateBuyEstimation();
    } else {
        updateSellEstimation();
    }
}

// 获取当前价格
function getCurrentPrice(type) {
    const priceType = document.querySelector(`input[name="${type}PriceType"]:checked`).value;

    if (priceType === 'market') {
        return selectedStock ? selectedStock.currentPrice : 0;
    } else {
        const limitPrice = parseFloat(document.getElementById(`${type}LimitPrice`).value) || 0;
        return limitPrice;
    }
}

// 更新买入估算
function updateBuyEstimation() {
    const quantity = parseInt(document.getElementById('buyQuantity').value) || 0;
    const price = getCurrentPrice('buy');

    const cost = quantity * price;
    const commission = cost * 0.0003; // 0.03% 手续费
    const totalCost = cost + commission;

    document.getElementById('estimatedCost').textContent = `¥${Utils.formatNumber(totalCost, 2)}`;
    document.getElementById('remainingFunds').textContent = `¥${Utils.formatNumber(availableFunds - totalCost, 2)}`;

    // 检查资金是否足够
    const remainingFunds = availableFunds - totalCost;
    if (remainingFunds < 0) {
        document.getElementById('remainingFunds').style.color = '#dc3545';
    } else {
        document.getElementById('remainingFunds').style.color = '#28a745';
    }
}

// 更新卖出估算
function updateSellEstimation() {
    const quantity = parseInt(document.getElementById('sellQuantity').value) || 0;
    const sellStockCode = document.getElementById('sellStockCode').value;

    if (!sellStockCode) return;

    const stock = stocksData.find(s => s.code === sellStockCode);
    if (!stock) return;

    const price = getCurrentPrice('sell');
    const income = quantity * price;
    const commission = income * 0.0003; // 0.03% 手续费
    const stampTax = income * 0.001; // 0.1% 印花税
    const totalIncome = income - commission - stampTax;

    document.getElementById('estimatedIncome').textContent = `¥${Utils.formatNumber(totalIncome, 2)}`;
}

// 处理卖出股票变化
function handleSellStockChange() {
    const sellStockCode = document.getElementById('sellStockCode').value;

    if (sellStockCode) {
        const holding = portfolioData.find(h => h.code === sellStockCode);
        const stock = stocksData.find(s => s.code === sellStockCode);

        if (holding && stock) {
            document.getElementById('holdingQuantity').textContent = `${holding.quantity} 股`;
            document.getElementById('holdingCostPrice').textContent = `¥${holding.costPrice.toFixed(2)}`;
            document.getElementById('maxSellHint').textContent = `最大可卖：${holding.quantity} 股`;

            // 设置卖出数量的最大值
            document.getElementById('sellQuantity').max = holding.quantity;

            // 更新选中的股票信息
            selectStockForTrading(sellStockCode);
        }
    } else {
        document.getElementById('holdingQuantity').textContent = '0 股';
        document.getElementById('holdingCostPrice').textContent = '¥0.00';
        document.getElementById('maxSellHint').textContent = '最大可卖：0 股';
    }

    updateSellEstimation();
    validateSellForm();
}

// 验证买入表单
function validateBuyForm() {
    const stockCode = document.getElementById('buyStockCode').value;
    const quantity = parseInt(document.getElementById('buyQuantity').value) || 0;
    const price = getCurrentPrice('buy');

    const isValid = stockCode &&
                   quantity >= 100 &&
                   quantity % 100 === 0 &&
                   price > 0 &&
                   (quantity * price * 1.0003) <= availableFunds;

    document.getElementById('buySubmitBtn').disabled = !isValid;
}

// 验证卖出表单
function validateSellForm() {
    const sellStockCode = document.getElementById('sellStockCode').value;
    const quantity = parseInt(document.getElementById('sellQuantity').value) || 0;

    let isValid = false;

    if (sellStockCode && quantity >= 100 && quantity % 100 === 0) {
        const holding = portfolioData.find(h => h.code === sellStockCode);
        if (holding && quantity <= holding.quantity) {
            isValid = true;
        }
    }

    document.getElementById('sellSubmitBtn').disabled = !isValid;
}

// 加载持仓股票
function loadHoldingStocks() {
    const sellStockSelect = document.getElementById('sellStockCode');

    sellStockSelect.innerHTML = '<option value="">请选择持仓股票</option>';

    portfolioData.forEach(holding => {
        const option = document.createElement('option');
        option.value = holding.code;
        option.textContent = `${holding.code} ${holding.name} (${holding.quantity}股)`;
        sellStockSelect.appendChild(option);
    });
}

// 更新账户信息
function updateAccountInfo() {
    // 计算持仓市值
    let totalMarketValue = 0;
    let totalProfit = 0;

    portfolioData.forEach(holding => {
        const marketValue = holding.quantity * holding.currentPrice;
        const profit = (holding.currentPrice - holding.costPrice) * holding.quantity;
        totalMarketValue += marketValue;
        totalProfit += profit;
    });

    // 更新显示
    const accountItems = document.querySelectorAll('.account-item');
    accountItems[0].querySelector('.account-value').textContent = `¥${Utils.formatNumber(availableFunds + totalMarketValue, 2)}`;
    accountItems[1].querySelector('.account-value').textContent = `¥${Utils.formatNumber(availableFunds, 2)}`;
    accountItems[2].querySelector('.account-value').textContent = `¥${Utils.formatNumber(totalMarketValue, 2)}`;

    const profitElement = accountItems[3].querySelector('.account-value');
    const profitSymbol = totalProfit >= 0 ? '+' : '';
    profitElement.textContent = `${profitSymbol}¥${Utils.formatNumber(totalProfit, 2)}`;
    profitElement.className = `account-value ${totalProfit >= 0 ? 'profit' : 'loss'}`;

    // 更新可用资金显示
    document.getElementById('availableFunds').textContent = `¥${Utils.formatNumber(availableFunds, 2)}`;
}

// 处理买入订单
function handleBuyOrder(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const stockCode = formData.get('stockCode');
    const quantity = parseInt(formData.get('quantity'));
    const priceType = formData.get('buyPriceType');
    const limitPrice = parseFloat(formData.get('limitPrice')) || 0;

    const stock = stocksData.find(s => s.code === stockCode);
    if (!stock) {
        addTradingResult(false, '买入失败', '股票代码不存在');
        return;
    }

    const price = priceType === 'market' ? stock.currentPrice : limitPrice;
    const cost = quantity * price;
    const commission = cost * 0.0003;
    const totalCost = cost + commission;

    // 检查资金是否足够
    if (totalCost > availableFunds) {
        addTradingResult(false, '买入失败', '可用资金不足');
        return;
    }

    // 模拟交易执行
    setTimeout(() => {
        // 扣除资金
        availableFunds -= totalCost;

        // 更新持仓
        const existingHolding = portfolioData.find(h => h.code === stockCode);
        if (existingHolding) {
            // 计算新的成本价
            const totalQuantity = existingHolding.quantity + quantity;
            const totalCost = (existingHolding.quantity * existingHolding.costPrice) + (quantity * price);
            existingHolding.costPrice = totalCost / totalQuantity;
            existingHolding.quantity = totalQuantity;
            existingHolding.currentPrice = stock.currentPrice;
        } else {
            portfolioData.push({
                code: stockCode,
                name: stock.name,
                quantity: quantity,
                costPrice: price,
                currentPrice: stock.currentPrice
            });
        }

        // 添加交易结果
        addTradingResult(true, '买入成功',
            `${stockCode} ${stock.name} ${quantity}股 @¥${price.toFixed(2)}`);

        // 更新界面
        updateAccountInfo();
        loadHoldingStocks();
        resetForm();

    }, 1000); // 模拟1秒延迟
}

// 处理卖出订单
function handleSellOrder(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const stockCode = formData.get('stockCode');
    const quantity = parseInt(formData.get('quantity'));
    const priceType = formData.get('sellPriceType');
    const limitPrice = parseFloat(formData.get('limitPrice')) || 0;

    const stock = stocksData.find(s => s.code === stockCode);
    const holding = portfolioData.find(h => h.code === stockCode);

    if (!stock || !holding) {
        addTradingResult(false, '卖出失败', '股票不存在或无持仓');
        return;
    }

    if (quantity > holding.quantity) {
        addTradingResult(false, '卖出失败', '卖出数量超过持仓数量');
        return;
    }

    const price = priceType === 'market' ? stock.currentPrice : limitPrice;
    const income = quantity * price;
    const commission = income * 0.0003;
    const stampTax = income * 0.001;
    const totalIncome = income - commission - stampTax;

    // 模拟交易执行
    setTimeout(() => {
        // 增加资金
        availableFunds += totalIncome;

        // 更新持仓
        holding.quantity -= quantity;
        if (holding.quantity === 0) {
            const index = portfolioData.indexOf(holding);
            portfolioData.splice(index, 1);
        }

        // 添加交易结果
        addTradingResult(true, '卖出成功',
            `${stockCode} ${stock.name} ${quantity}股 @¥${price.toFixed(2)}`);

        // 更新界面
        updateAccountInfo();
        loadHoldingStocks();
        resetForm();

    }, 1000); // 模拟1秒延迟
}

// 添加交易结果
function addTradingResult(success, title, details) {
    const result = {
        id: Date.now(),
        success: success,
        title: title,
        details: details,
        time: new Date().toLocaleString()
    };

    tradingResults.unshift(result);
    renderTradingResults();
}

// 渲染交易结果
function renderTradingResults() {
    const resultsContainer = document.getElementById('tradingResults');

    if (tradingResults.length === 0) {
        resultsContainer.innerHTML = `
            <div class="no-results">
                <i class="fas fa-clipboard-list"></i>
                <p>暂无交易记录</p>
            </div>
        `;
        return;
    }

    resultsContainer.innerHTML = tradingResults.map(result => `
        <div class="result-item">
            <div class="result-icon ${result.success ? 'success' : 'error'}">
                <i class="fas fa-${result.success ? 'check' : 'times'}"></i>
            </div>
            <div class="result-content">
                <div class="result-title">${result.title}</div>
                <div class="result-details">${result.details}</div>
                <div class="result-time">${result.time}</div>
            </div>
        </div>
    `).join('');
}

// 清空交易记录
function clearResults() {
    if (tradingResults.length === 0) {
        Toast.show('暂无交易记录', 'info');
        return;
    }

    if (confirm('确定要清空所有交易记录吗？')) {
        tradingResults = [];
        renderTradingResults();
        Toast.show('交易记录已清空', 'success');
    }
}

// 导出交易记录
function exportResults() {
    if (tradingResults.length === 0) {
        Toast.show('暂无交易记录可导出', 'info');
        return;
    }

    const csvContent = [
        ['时间', '类型', '详情', '状态'],
        ...tradingResults.map(result => [
            result.time,
            result.title,
            result.details,
            result.success ? '成功' : '失败'
        ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `交易记录_${Utils.formatDate(new Date(), 'YYYY-MM-DD_HH-mm-ss')}.csv`;
    link.click();

    Toast.show('交易记录已导出', 'success');
}

// 打开股票选择器
function openStockSelector(type) {
    currentSelectorType = type;
    const modal = document.getElementById('stockSelectorModal');
    const selectorList = document.getElementById('stockSelectorList');

    // 渲染股票列表
    renderStockSelectorList(stocksData);

    // 显示模态框
    modal.style.display = 'flex';

    // 清空搜索框
    document.getElementById('selectorSearch').value = '';
}

// 关闭股票选择器
function closeStockSelector() {
    const modal = document.getElementById('stockSelectorModal');
    modal.style.display = 'none';
}

// 处理选择器搜索
function handleSelectorSearch() {
    const searchTerm = document.getElementById('selectorSearch').value.toLowerCase();

    if (!searchTerm) {
        renderStockSelectorList(stocksData);
        return;
    }

    const filteredStocks = stocksData.filter(stock =>
        stock.code.toLowerCase().includes(searchTerm) ||
        stock.name.toLowerCase().includes(searchTerm)
    );

    renderStockSelectorList(filteredStocks);
}

// 渲染股票选择器列表
function renderStockSelectorList(stocks) {
    const selectorList = document.getElementById('stockSelectorList');

    if (stocks.length === 0) {
        selectorList.innerHTML = `
            <div style="text-align: center; padding: 2rem; color: #666;">
                <i class="fas fa-search"></i>
                <p>未找到相关股票</p>
            </div>
        `;
        return;
    }

    selectorList.innerHTML = stocks.map(stock => {
        const changeClass = stock.change >= 0 ? 'price-up' : 'price-down';
        const changeSymbol = stock.change >= 0 ? '+' : '';

        return `
            <div class="stock-selector-item" onclick="selectFromSelector('${stock.code}')">
                <div class="stock-info-left">
                    <div class="stock-code">${stock.code}</div>
                    <div class="stock-name">${stock.name}</div>
                </div>
                <div class="stock-info-right">
                    <div class="current-price ${changeClass}">${stock.currentPrice.toFixed(2)}</div>
                    <div class="price-change ${changeClass}">
                        ${changeSymbol}${stock.change.toFixed(2)} (${changeSymbol}${stock.changePercent.toFixed(2)}%)
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// 从选择器中选择股票
function selectFromSelector(stockCode) {
    if (currentSelectorType === 'buy') {
        document.getElementById('buyStockCode').value = stockCode;
        selectStockForTrading(stockCode);
        validateBuyForm();
    }

    closeStockSelector();
}

// 点击模态框外部关闭
document.addEventListener('click', function(event) {
    const modal = document.getElementById('stockSelectorModal');
    if (event.target === modal) {
        closeStockSelector();
    }
});

// 键盘事件处理
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const modal = document.getElementById('stockSelectorModal');
        if (modal.style.display === 'flex') {
            closeStockSelector();
        }
    }
});
