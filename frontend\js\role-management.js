// 角色管理页面JavaScript

// 模拟数据
let rolesData = [
    {
        id: 'R001',
        name: '系统管理员',
        description: '拥有系统所有权限，可以管理用户、角色和系统配置',
        status: 'enabled',
        createTime: '2024-01-15 09:30:00',
        userCount: 3
    },
    {
        id: 'R002',
        name: '普通用户',
        description: '基础用户权限，可以查看股票信息和进行模拟交易',
        status: 'enabled',
        createTime: '2024-01-15 10:15:00',
        userCount: 156
    },
    {
        id: 'R003',
        name: '数据分析师',
        description: '可以访问高级数据分析功能和生成报告',
        status: 'enabled',
        createTime: '2024-01-16 14:20:00',
        userCount: 12
    },
    {
        id: 'R004',
        name: '交易员',
        description: '专业交易权限，可以执行高级交易操作',
        status: 'enabled',
        createTime: '2024-01-17 11:45:00',
        userCount: 28
    },
    {
        id: 'R005',
        name: '审计员',
        description: '审计权限，可以查看系统日志和用户操作记录',
        status: 'disabled',
        createTime: '2024-01-18 16:30:00',
        userCount: 5
    },
    {
        id: 'R006',
        name: '客服代表',
        description: '客户服务权限，可以处理用户问题和查询',
        status: 'enabled',
        createTime: '2024-01-19 09:00:00',
        userCount: 15
    },
    {
        id: 'R007',
        name: '风控专员',
        description: '风险控制权限，监控交易风险和异常行为',
        status: 'enabled',
        createTime: '2024-01-20 13:15:00',
        userCount: 8
    },
    {
        id: 'R008',
        name: '测试用户',
        description: '测试环境专用角色，用于系统测试',
        status: 'disabled',
        createTime: '2024-01-21 10:30:00',
        userCount: 2
    }
];

// 全局变量
let currentPage = 1;
const pageSize = 10;
let filteredData = [...rolesData];
let selectedRoles = new Set();
let editingRole = null;

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    bindEvents();
    renderTable();
    updateStats();
});

// 初始化页面
function initializePage() {
    // 初始化搜索
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    
    searchInput.addEventListener('input', Utils.debounce(handleSearch, 300));
    statusFilter.addEventListener('change', handleSearch);
    
    // 初始化表单验证
    const validator = new FormValidator('roleForm');
    validator
        .addRule('roleName', {
            required: true,
            minLength: 2,
            requiredMessage: '角色名称不能为空',
            minLengthMessage: '角色名称至少需要2个字符'
        });
}

// 绑定事件
function bindEvents() {
    // 表单提交
    document.getElementById('roleForm').addEventListener('submit', handleFormSubmit);
    
    // 全选复选框
    document.getElementById('selectAll').addEventListener('change', toggleSelectAll);
}

// 处理搜索
function handleSearch() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    
    filteredData = rolesData.filter(role => {
        const matchesSearch = !searchTerm || 
            role.name.toLowerCase().includes(searchTerm) ||
            role.description.toLowerCase().includes(searchTerm);
        
        const matchesStatus = !statusFilter || role.status === statusFilter;
        
        return matchesSearch && matchesStatus;
    });
    
    currentPage = 1;
    selectedRoles.clear();
    updateSelectButtons();
    renderTable();
    updateStats();
}

// 渲染表格
function renderTable() {
    const tbody = document.getElementById('rolesTableBody');
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pageData = filteredData.slice(startIndex, endIndex);
    
    if (pageData.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <h3>暂无数据</h3>
                    <p>没有找到符合条件的角色</p>
                </td>
            </tr>
        `;
    } else {
        tbody.innerHTML = pageData.map(role => `
            <tr>
                <td>
                    <input type="checkbox" value="${role.id}" onchange="handleRowSelect('${role.id}')">
                </td>
                <td>${role.id}</td>
                <td>
                    <strong>${role.name}</strong>
                    <div style="font-size: 0.8rem; color: #666; margin-top: 0.25rem;">
                        ${role.userCount} 个用户
                    </div>
                </td>
                <td>
                    <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" 
                         title="${role.description}">
                        ${role.description}
                    </div>
                </td>
                <td>
                    <span class="status-badge ${role.status}">
                        ${role.status === 'enabled' ? '启用' : '禁用'}
                    </span>
                </td>
                <td>${role.createTime}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="editRole('${role.id}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-danger" onclick="deleteRole('${role.id}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button class="btn btn-secondary" onclick="toggleRoleStatus('${role.id}')" title="切换状态">
                            <i class="fas fa-${role.status === 'enabled' ? 'ban' : 'check'}"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    updatePagination();
    updateSelectAll();
}

// 更新分页
function updatePagination() {
    const totalPages = Math.ceil(filteredData.length / pageSize);
    const pageInfo = document.getElementById('pageInfo');
    const pageNumbers = document.getElementById('pageNumbers');
    const prevBtn = document.getElementById('prevPage');
    const nextBtn = document.getElementById('nextPage');
    
    // 更新页面信息
    const start = (currentPage - 1) * pageSize + 1;
    const end = Math.min(currentPage * pageSize, filteredData.length);
    pageInfo.textContent = `显示 ${start}-${end} 条，共 ${filteredData.length} 条`;
    
    // 更新按钮状态
    prevBtn.disabled = currentPage <= 1;
    nextBtn.disabled = currentPage >= totalPages;
    
    // 生成页码
    let pagesHtml = '';
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
        pagesHtml += `
            <a href="#" class="page-number ${i === currentPage ? 'active' : ''}" 
               onclick="goToPage(${i})">${i}</a>
        `;
    }
    
    pageNumbers.innerHTML = pagesHtml;
}

// 更新统计信息
function updateStats() {
    const total = filteredData.length;
    const enabled = filteredData.filter(role => role.status === 'enabled').length;
    const disabled = total - enabled;
    
    document.getElementById('totalRoles').textContent = total;
    document.getElementById('enabledRoles').textContent = enabled;
    document.getElementById('disabledRoles').textContent = disabled;
}

// 处理行选择
function handleRowSelect(roleId) {
    const checkbox = document.querySelector(`input[value="${roleId}"]`);
    if (checkbox.checked) {
        selectedRoles.add(roleId);
    } else {
        selectedRoles.delete(roleId);
    }
    updateSelectButtons();
    updateSelectAll();
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const rowCheckboxes = document.querySelectorAll('#rolesTableBody input[type="checkbox"]');
    
    selectedRoles.clear();
    
    rowCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
        if (selectAllCheckbox.checked) {
            selectedRoles.add(checkbox.value);
        }
    });
    
    updateSelectButtons();
}

// 更新全选状态
function updateSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const rowCheckboxes = document.querySelectorAll('#rolesTableBody input[type="checkbox"]');
    const checkedCount = document.querySelectorAll('#rolesTableBody input[type="checkbox"]:checked').length;
    
    if (checkedCount === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (checkedCount === rowCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
        selectAllCheckbox.checked = false;
    }
}

// 更新选择按钮状态
function updateSelectButtons() {
    const editBtn = document.getElementById('editBtn');
    const deleteBtn = document.getElementById('deleteBtn');
    const hasSelection = selectedRoles.size > 0;
    
    editBtn.disabled = selectedRoles.size !== 1;
    deleteBtn.disabled = !hasSelection;
}

// 分页函数
function prevPage() {
    if (currentPage > 1) {
        currentPage--;
        renderTable();
    }
}

function nextPage() {
    const totalPages = Math.ceil(filteredData.length / pageSize);
    if (currentPage < totalPages) {
        currentPage++;
        renderTable();
    }
}

function goToPage(page) {
    currentPage = page;
    renderTable();
}

// 显示新增模态框
function showAddModal() {
    editingRole = null;
    document.getElementById('modalTitle').textContent = '新增角色';
    document.getElementById('roleForm').reset();
    document.getElementById('roleStatus').value = 'enabled';
    new Modal('roleModal').show();
}

// 编辑角色
function editRole(roleId) {
    const role = rolesData.find(r => r.id === roleId);
    if (!role) return;
    
    editingRole = role;
    document.getElementById('modalTitle').textContent = '编辑角色';
    document.getElementById('roleName').value = role.name;
    document.getElementById('roleDescription').value = role.description;
    document.getElementById('roleStatus').value = role.status;
    
    new Modal('roleModal').show();
}

// 编辑选中的角色
function editSelected() {
    if (selectedRoles.size === 1) {
        const roleId = Array.from(selectedRoles)[0];
        editRole(roleId);
    }
}

// 隐藏角色模态框
function hideRoleModal() {
    new Modal('roleModal').hide();
}

// 处理表单提交
function handleFormSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const roleData = {
        name: formData.get('roleName'),
        description: formData.get('roleDescription'),
        status: formData.get('roleStatus')
    };
    
    if (editingRole) {
        // 更新现有角色
        Object.assign(editingRole, roleData);
        Toast.show('角色更新成功！', 'success');
    } else {
        // 创建新角色
        const newRole = {
            id: 'R' + String(rolesData.length + 1).padStart(3, '0'),
            ...roleData,
            createTime: Utils.formatDate(new Date()),
            userCount: 0
        };
        rolesData.push(newRole);
        Toast.show('角色创建成功！', 'success');
    }
    
    hideRoleModal();
    handleSearch(); // 重新筛选和渲染
}

// 删除角色
function deleteRole(roleId) {
    const role = rolesData.find(r => r.id === roleId);
    if (!role) return;
    
    document.getElementById('deleteRoleName').textContent = role.name;
    window.roleToDelete = roleId;
    new Modal('deleteModal').show();
}

// 删除选中的角色
function deleteSelected() {
    if (selectedRoles.size === 0) return;
    
    const roleNames = Array.from(selectedRoles)
        .map(id => rolesData.find(r => r.id === id)?.name)
        .filter(Boolean)
        .join('、');
    
    document.getElementById('deleteRoleName').textContent = roleNames;
    window.rolesToDelete = Array.from(selectedRoles);
    new Modal('deleteModal').show();
}

// 隐藏删除模态框
function hideDeleteModal() {
    new Modal('deleteModal').hide();
}

// 确认删除
function confirmDelete() {
    const rolesToDelete = window.rolesToDelete || [window.roleToDelete];
    
    rolesToDelete.forEach(roleId => {
        const index = rolesData.findIndex(r => r.id === roleId);
        if (index > -1) {
            rolesData.splice(index, 1);
        }
    });
    
    selectedRoles.clear();
    updateSelectButtons();
    hideDeleteModal();
    handleSearch(); // 重新筛选和渲染
    
    Toast.show(`成功删除 ${rolesToDelete.length} 个角色！`, 'success');
}

// 切换角色状态
function toggleRoleStatus(roleId) {
    const role = rolesData.find(r => r.id === roleId);
    if (!role) return;
    
    role.status = role.status === 'enabled' ? 'disabled' : 'enabled';
    renderTable();
    updateStats();
    
    const statusText = role.status === 'enabled' ? '启用' : '禁用';
    Toast.show(`角色 "${role.name}" 已${statusText}！`, 'success');
}

// 刷新表格
function refreshTable() {
    // 模拟刷新数据
    Toast.show('数据已刷新！', 'info');
    renderTable();
    updateStats();
}

// 导出数据
function exportData() {
    // 模拟导出功能
    const csvContent = "data:text/csv;charset=utf-8,"
        + "角色ID,角色名称,角色描述,状态,创建时间,用户数量\n"
        + filteredData.map(role =>
            `${role.id},${role.name},"${role.description}",${role.status === 'enabled' ? '启用' : '禁用'},${role.createTime},${role.userCount}`
        ).join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `角色管理数据_${Utils.formatDate(new Date(), 'YYYY-MM-DD')}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    Toast.show('数据导出成功！', 'success');
}
