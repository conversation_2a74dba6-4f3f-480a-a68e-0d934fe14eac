// 功能管理页面JavaScript

// 模拟功能数据
let functionsData = [
    {
        id: 'F001',
        name: '业务子系统',
        code: 'business_system',
        url: '',
        description: '业务相关功能模块',
        icon: 'fas fa-briefcase',
        sort: 1,
        enabled: true,
        parentId: null,
        children: [
            {
                id: 'F001001',
                name: '股票买卖',
                code: 'stock_trading',
                url: '/stock/trading',
                description: '股票交易相关功能',
                icon: 'fas fa-chart-line',
                sort: 1,
                enabled: true,
                parentId: 'F001',
                children: [
                    {
                        id: 'F001001001',
                        name: '买入操作',
                        code: 'stock_buy',
                        url: '/stock/buy',
                        description: '股票买入功能',
                        icon: 'fas fa-arrow-up',
                        sort: 1,
                        enabled: true,
                        parentId: 'F001001'
                    },
                    {
                        id: 'F001001002',
                        name: '卖出操作',
                        code: 'stock_sell',
                        url: '/stock/sell',
                        description: '股票卖出功能',
                        icon: 'fas fa-arrow-down',
                        sort: 2,
                        enabled: true,
                        parentId: 'F001001'
                    },
                    {
                        id: 'F001001003',
                        name: '实时行情',
                        code: 'stock_realtime',
                        url: '/stock/realtime',
                        description: '实时股票行情查看',
                        icon: 'fas fa-chart-candlestick',
                        sort: 3,
                        enabled: true,
                        parentId: 'F001001'
                    }
                ]
            },
            {
                id: 'F001002',
                name: '公司信息查询',
                code: 'company_info',
                url: '/company/info',
                description: '上市公司基本信息查询',
                icon: 'fas fa-building',
                sort: 2,
                enabled: true,
                parentId: 'F001',
                children: [
                    {
                        id: 'F001002001',
                        name: '基本信息',
                        code: 'company_basic',
                        url: '/company/basic',
                        description: '公司基本信息查看',
                        icon: 'fas fa-info-circle',
                        sort: 1,
                        enabled: true,
                        parentId: 'F001002'
                    },
                    {
                        id: 'F001002002',
                        name: '财务数据',
                        code: 'company_finance',
                        url: '/company/finance',
                        description: '公司财务数据查看',
                        icon: 'fas fa-chart-bar',
                        sort: 2,
                        enabled: true,
                        parentId: 'F001002'
                    }
                ]
            }
        ]
    },
    {
        id: 'F002',
        name: '用户权限子系统',
        code: 'permission_system',
        url: '',
        description: '用户权限管理功能',
        icon: 'fas fa-shield-alt',
        sort: 2,
        enabled: true,
        parentId: null,
        children: [
            {
                id: 'F002001',
                name: '角色管理',
                code: 'role_management',
                url: '/role/management',
                description: '系统角色管理',
                icon: 'fas fa-users-cog',
                sort: 1,
                enabled: true,
                parentId: 'F002'
            },
            {
                id: 'F002002',
                name: '功能管理',
                code: 'function_management',
                url: '/function/management',
                description: '系统功能管理',
                icon: 'fas fa-cogs',
                sort: 2,
                enabled: true,
                parentId: 'F002'
            },
            {
                id: 'F002003',
                name: '角色分配管理',
                code: 'role_assignment',
                url: '/role/assignment',
                description: '用户角色分配',
                icon: 'fas fa-user-tag',
                sort: 3,
                enabled: true,
                parentId: 'F002'
            },
            {
                id: 'F002004',
                name: '权限分配管理',
                code: 'permission_assignment',
                url: '/permission/assignment',
                description: '角色权限分配',
                icon: 'fas fa-key',
                sort: 4,
                enabled: false,
                parentId: 'F002'
            }
        ]
    }
];

// 常用图标列表
const commonIcons = [
    'fas fa-home', 'fas fa-user', 'fas fa-users', 'fas fa-cog', 'fas fa-chart-line',
    'fas fa-chart-bar', 'fas fa-chart-pie', 'fas fa-briefcase', 'fas fa-building',
    'fas fa-shield-alt', 'fas fa-key', 'fas fa-lock', 'fas fa-unlock', 'fas fa-eye',
    'fas fa-edit', 'fas fa-trash', 'fas fa-plus', 'fas fa-minus', 'fas fa-save',
    'fas fa-download', 'fas fa-upload', 'fas fa-search', 'fas fa-filter', 'fas fa-sort',
    'fas fa-arrow-up', 'fas fa-arrow-down', 'fas fa-arrow-left', 'fas fa-arrow-right',
    'fas fa-check', 'fas fa-times', 'fas fa-exclamation', 'fas fa-question',
    'fas fa-info-circle', 'fas fa-warning', 'fas fa-bell', 'fas fa-envelope',
    'fas fa-phone', 'fas fa-calendar', 'fas fa-clock', 'fas fa-map-marker-alt'
];

// 全局变量
let selectedFunctions = new Set();
let currentSelectedFunction = null;
let expandedNodes = new Set();

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    bindEvents();
    renderFunctionTree();
    updateBatchOperations();
});

// 初始化页面
function initializePage() {
    // 初始化搜索
    const searchInput = document.getElementById('functionSearch');
    searchInput.addEventListener('input', Utils.debounce(handleSearch, 300));
    
    // 初始化表单验证
    const validator = new FormValidator('addFunctionForm');
    validator
        .addRule('functionName', {
            required: true,
            minLength: 2,
            requiredMessage: '功能名称不能为空',
            minLengthMessage: '功能名称至少需要2个字符'
        })
        .addRule('functionCode', {
            required: true,
            pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/,
            requiredMessage: '功能标识不能为空',
            patternMessage: '功能标识只能包含字母、数字和下划线，且必须以字母开头'
        });
}

// 绑定事件
function bindEvents() {
    // 表单提交
    document.getElementById('addFunctionForm').addEventListener('submit', handleFormSubmit);
}

// 渲染功能树
function renderFunctionTree() {
    const treeContainer = document.getElementById('functionTree');
    treeContainer.innerHTML = '';
    
    functionsData.forEach(rootFunction => {
        const nodeElement = createTreeNode(rootFunction, 0);
        treeContainer.appendChild(nodeElement);
    });
}

// 创建树节点
function createTreeNode(functionData, level) {
    const nodeDiv = document.createElement('div');
    nodeDiv.className = 'tree-node';
    nodeDiv.setAttribute('data-function-id', functionData.id);
    
    const hasChildren = functionData.children && functionData.children.length > 0;
    const isExpanded = expandedNodes.has(functionData.id);
    const isSelected = currentSelectedFunction === functionData.id;
    const isChecked = selectedFunctions.has(functionData.id);
    
    nodeDiv.innerHTML = `
        <div class="tree-item ${isSelected ? 'selected' : ''} ${!functionData.enabled ? 'disabled' : ''}" 
             onclick="selectFunction('${functionData.id}')">
            <div class="tree-toggle ${hasChildren ? (isExpanded ? 'expanded' : '') : 'empty'}" 
                 onclick="event.stopPropagation(); toggleNode('${functionData.id}')">
                ${hasChildren ? '<i class="fas fa-chevron-right"></i>' : ''}
            </div>
            <input type="checkbox" class="tree-checkbox" ${isChecked ? 'checked' : ''} 
                   onchange="event.stopPropagation(); toggleFunctionSelection('${functionData.id}')" />
            <i class="item-icon ${functionData.icon}"></i>
            <div class="item-content">
                <div>
                    <span class="item-name">${functionData.name}</span>
                    <span class="item-code">${functionData.code}</span>
                </div>
                <div class="item-status">
                    <div class="status-toggle ${functionData.enabled ? 'enabled' : ''}" 
                         onclick="event.stopPropagation(); toggleFunctionStatus('${functionData.id}')"></div>
                </div>
            </div>
        </div>
    `;
    
    // 添加子节点
    if (hasChildren) {
        const childrenDiv = document.createElement('div');
        childrenDiv.className = `tree-children ${isExpanded ? 'expanded' : ''}`;
        
        functionData.children.forEach(child => {
            const childNode = createTreeNode(child, level + 1);
            childrenDiv.appendChild(childNode);
        });
        
        nodeDiv.appendChild(childrenDiv);
    }
    
    return nodeDiv;
}

// 切换节点展开/收起
function toggleNode(functionId) {
    if (expandedNodes.has(functionId)) {
        expandedNodes.delete(functionId);
    } else {
        expandedNodes.add(functionId);
    }
    renderFunctionTree();
}

// 展开所有节点
function expandAll() {
    const addAllIds = (functions) => {
        functions.forEach(func => {
            if (func.children && func.children.length > 0) {
                expandedNodes.add(func.id);
                addAllIds(func.children);
            }
        });
    };
    
    addAllIds(functionsData);
    renderFunctionTree();
}

// 收起所有节点
function collapseAll() {
    expandedNodes.clear();
    renderFunctionTree();
}

// 选择功能
function selectFunction(functionId) {
    currentSelectedFunction = functionId;
    renderFunctionTree();
    showFunctionConfig(functionId);
}

// 显示功能配置
function showFunctionConfig(functionId) {
    const functionData = findFunctionById(functionId);
    if (!functionData) return;
    
    const configContent = document.getElementById('configContent');
    configContent.innerHTML = `
        <div class="config-header">
            <div class="function-icon">
                <i class="${functionData.icon}"></i>
            </div>
            <div class="function-info">
                <h4>${functionData.name}</h4>
                <p>${functionData.code}</p>
            </div>
        </div>
        
        <form class="config-form">
            <div class="form-group">
                <label class="form-label">功能名称</label>
                <input type="text" class="form-control" value="${functionData.name}" 
                       onchange="updateFunction('${functionId}', 'name', this.value)">
            </div>
            <div class="form-group">
                <label class="form-label">功能标识</label>
                <input type="text" class="form-control" value="${functionData.code}" 
                       onchange="updateFunction('${functionId}', 'code', this.value)">
            </div>
            <div class="form-group">
                <label class="form-label">功能URL</label>
                <input type="text" class="form-control" value="${functionData.url || ''}" 
                       onchange="updateFunction('${functionId}', 'url', this.value)">
            </div>
            <div class="form-group">
                <label class="form-label">功能描述</label>
                <textarea class="form-control" rows="3" 
                          onchange="updateFunction('${functionId}', 'description', this.value)">${functionData.description || ''}</textarea>
            </div>
            <div class="form-group">
                <label class="form-label">图标</label>
                <div class="icon-selector">
                    <input type="text" class="form-control" value="${functionData.icon}" readonly>
                    <button type="button" class="btn btn-secondary" onclick="showIconPicker('${functionId}')">
                        <i class="fas fa-icons"></i> 选择图标
                    </button>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">排序</label>
                <input type="number" class="form-control" value="${functionData.sort}" min="0"
                       onchange="updateFunction('${functionId}', 'sort', parseInt(this.value))">
            </div>
            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" ${functionData.enabled ? 'checked' : ''} 
                           onchange="updateFunction('${functionId}', 'enabled', this.checked)">
                    <label class="form-check-label">启用此功能</label>
                </div>
            </div>
            <div class="form-group">
                <button type="button" class="btn btn-danger" onclick="deleteFunction('${functionId}')">
                    <i class="fas fa-trash"></i> 删除功能
                </button>
            </div>
        </form>
    `;
}

// 查找功能数据
function findFunctionById(functionId) {
    const findInArray = (functions) => {
        for (const func of functions) {
            if (func.id === functionId) {
                return func;
            }
            if (func.children) {
                const found = findInArray(func.children);
                if (found) return found;
            }
        }
        return null;
    };
    
    return findInArray(functionsData);
}

// 更新功能数据
function updateFunction(functionId, field, value) {
    const functionData = findFunctionById(functionId);
    if (functionData) {
        functionData[field] = value;
        if (field === 'name' || field === 'enabled' || field === 'icon') {
            renderFunctionTree();
        }
        Toast.show('功能信息已更新', 'success');
    }
}

// 切换功能选择
function toggleFunctionSelection(functionId) {
    if (selectedFunctions.has(functionId)) {
        selectedFunctions.delete(functionId);
    } else {
        selectedFunctions.add(functionId);
    }
    updateBatchOperations();
}

// 切换功能状态
function toggleFunctionStatus(functionId) {
    const functionData = findFunctionById(functionId);
    if (functionData) {
        functionData.enabled = !functionData.enabled;
        renderFunctionTree();
        if (currentSelectedFunction === functionId) {
            showFunctionConfig(functionId);
        }
        Toast.show(`功能已${functionData.enabled ? '启用' : '禁用'}`, 'success');
    }
}

// 更新批量操作
function updateBatchOperations() {
    const count = selectedFunctions.size;
    document.getElementById('selectedCount').textContent = count;
    
    const batchEnableBtn = document.getElementById('batchEnableBtn');
    const batchDisableBtn = document.getElementById('batchDisableBtn');
    const batchExportBtn = document.getElementById('batchExportBtn');
    
    batchEnableBtn.disabled = count === 0;
    batchDisableBtn.disabled = count === 0;
    batchExportBtn.disabled = count === 0;
}

// 处理搜索
function handleSearch() {
    const searchTerm = document.getElementById('functionSearch').value.toLowerCase();
    
    if (!searchTerm) {
        renderFunctionTree();
        return;
    }
    
    // 简单的搜索实现 - 在实际项目中可能需要更复杂的搜索逻辑
    const filterFunctions = (functions) => {
        return functions.filter(func => {
            const matches = func.name.toLowerCase().includes(searchTerm) ||
                          func.code.toLowerCase().includes(searchTerm) ||
                          (func.description && func.description.toLowerCase().includes(searchTerm));
            
            if (matches) return true;
            
            if (func.children) {
                const filteredChildren = filterFunctions(func.children);
                if (filteredChildren.length > 0) {
                    func.children = filteredChildren;
                    return true;
                }
            }
            
            return false;
        });
    };
    
    // 这里简化处理，实际应该创建过滤后的副本
    Toast.show('搜索功能开发中...', 'info');
}

// 显示新增功能模态框
function showAddFunctionModal() {
    // 填充父级功能选项
    const parentSelect = document.getElementById('parentFunction');
    parentSelect.innerHTML = '<option value="">选择父级功能（可选）</option>';

    const addOptions = (functions, prefix = '') => {
        functions.forEach(func => {
            const option = document.createElement('option');
            option.value = func.id;
            option.textContent = prefix + func.name;
            parentSelect.appendChild(option);

            if (func.children && func.children.length > 0) {
                addOptions(func.children, prefix + '　');
            }
        });
    };

    addOptions(functionsData);

    // 重置表单
    document.getElementById('addFunctionForm').reset();
    document.getElementById('functionEnabled').checked = true;

    new Modal('addFunctionModal').show();
}

// 隐藏新增功能模态框
function hideAddFunctionModal() {
    new Modal('addFunctionModal').hide();
}

// 处理表单提交
function handleFormSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const newFunction = {
        id: 'F' + Date.now(),
        name: formData.get('functionName'),
        code: formData.get('functionCode'),
        url: formData.get('functionUrl') || '',
        description: formData.get('functionDescription') || '',
        icon: formData.get('functionIcon') || 'fas fa-cog',
        sort: parseInt(formData.get('functionSort')) || 0,
        enabled: formData.has('functionEnabled'),
        parentId: formData.get('parentFunction') || null,
        children: []
    };

    // 添加到对应的父级或根级
    if (newFunction.parentId) {
        const parent = findFunctionById(newFunction.parentId);
        if (parent) {
            if (!parent.children) parent.children = [];
            parent.children.push(newFunction);
            parent.children.sort((a, b) => a.sort - b.sort);
        }
    } else {
        functionsData.push(newFunction);
        functionsData.sort((a, b) => a.sort - b.sort);
    }

    hideAddFunctionModal();
    renderFunctionTree();
    Toast.show('功能添加成功！', 'success');
}

// 删除功能
function deleteFunction(functionId) {
    if (!confirm('确定要删除这个功能吗？删除后将无法恢复。')) {
        return;
    }

    const removeFromArray = (functions) => {
        for (let i = 0; i < functions.length; i++) {
            if (functions[i].id === functionId) {
                functions.splice(i, 1);
                return true;
            }
            if (functions[i].children && removeFromArray(functions[i].children)) {
                return true;
            }
        }
        return false;
    };

    if (removeFromArray(functionsData)) {
        selectedFunctions.delete(functionId);
        if (currentSelectedFunction === functionId) {
            currentSelectedFunction = null;
            document.getElementById('configContent').innerHTML = `
                <div class="welcome-message">
                    <i class="fas fa-hand-point-left"></i>
                    <h4>选择功能进行配置</h4>
                    <p>请从左侧功能列表中选择一个功能来查看和编辑其配置信息</p>
                </div>
            `;
        }
        renderFunctionTree();
        updateBatchOperations();
        Toast.show('功能删除成功！', 'success');
    }
}

// 显示图标选择器
function showIconPicker(functionId) {
    window.currentIconFunction = functionId;

    const iconGrid = document.getElementById('iconGrid');
    iconGrid.innerHTML = commonIcons.map(icon => `
        <div class="icon-item" onclick="selectIcon('${icon}')">
            <i class="${icon}"></i>
            <span>${icon.split(' ')[1]}</span>
        </div>
    `).join('');

    // 绑定搜索事件
    const iconSearch = document.getElementById('iconSearch');
    iconSearch.addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();
        const filteredIcons = commonIcons.filter(icon =>
            icon.toLowerCase().includes(searchTerm)
        );

        iconGrid.innerHTML = filteredIcons.map(icon => `
            <div class="icon-item" onclick="selectIcon('${icon}')">
                <i class="${icon}"></i>
                <span>${icon.split(' ')[1]}</span>
            </div>
        `).join('');
    });

    new Modal('iconPickerModal').show();
}

// 选择图标
function selectIcon(iconClass) {
    const functionId = window.currentIconFunction;
    if (functionId) {
        updateFunction(functionId, 'icon', iconClass);
        showFunctionConfig(functionId);
    }
    hideIconPicker();
}

// 隐藏图标选择器
function hideIconPicker() {
    new Modal('iconPickerModal').hide();
}

// 批量启用
function batchEnable() {
    let count = 0;
    selectedFunctions.forEach(functionId => {
        const functionData = findFunctionById(functionId);
        if (functionData && !functionData.enabled) {
            functionData.enabled = true;
            count++;
        }
    });

    if (count > 0) {
        renderFunctionTree();
        if (currentSelectedFunction && selectedFunctions.has(currentSelectedFunction)) {
            showFunctionConfig(currentSelectedFunction);
        }
        Toast.show(`成功启用 ${count} 个功能！`, 'success');
    } else {
        Toast.show('所选功能均已启用', 'info');
    }
}

// 批量禁用
function batchDisable() {
    let count = 0;
    selectedFunctions.forEach(functionId => {
        const functionData = findFunctionById(functionId);
        if (functionData && functionData.enabled) {
            functionData.enabled = false;
            count++;
        }
    });

    if (count > 0) {
        renderFunctionTree();
        if (currentSelectedFunction && selectedFunctions.has(currentSelectedFunction)) {
            showFunctionConfig(currentSelectedFunction);
        }
        Toast.show(`成功禁用 ${count} 个功能！`, 'success');
    } else {
        Toast.show('所选功能均已禁用', 'info');
    }
}

// 批量导出
function batchExport() {
    const selectedData = Array.from(selectedFunctions).map(id => findFunctionById(id)).filter(Boolean);

    const csvContent = "data:text/csv;charset=utf-8,"
        + "功能ID,功能名称,功能标识,功能URL,功能描述,图标,排序,状态\n"
        + selectedData.map(func =>
            `${func.id},${func.name},${func.code},"${func.url}","${func.description}",${func.icon},${func.sort},${func.enabled ? '启用' : '禁用'}`
        ).join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `功能数据_${Utils.formatDate(new Date(), 'YYYY-MM-DD')}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    Toast.show('数据导出成功！', 'success');
}

// 清除选择
function clearSelection() {
    selectedFunctions.clear();
    renderFunctionTree();
    updateBatchOperations();
    Toast.show('已清除所有选择', 'info');
}

// 保存所有更改
function saveAllChanges() {
    // 模拟保存到服务器
    Toast.show('所有更改已保存！', 'success');
}
