// 公司基本信息查询页面JavaScript

// 模拟公司数据
let companiesData = {
    '000001': {
        code: '000001',
        name: '平安银行',
        fullName: '平安银行股份有限公司',
        market: '深圳证券交易所',
        industry: '银行业',
        establishDate: '1987-12-22',
        listingDate: '1991-04-03',
        registeredCapital: '194.06亿元',
        legalRepresentative: '谢永林',
        address: '广东省深圳市罗湖区深南东路5047号',
        phone: '0755-82088888',
        website: 'www.pingan.com',
        email: '<EMAIL>',
        postalCode: '518001',
        investorRelations: '0755-82088888',
        totalShares: '194.06亿股',
        circulatingShares: '194.06亿股',
        marketCap: '2493.17亿元',
        chairman: '谢永林',
        generalManager: '胡跃飞',
        boardSecretary: '陈蓉',
        cfo: '项有志',
        employeeCount: '45,678人',
        independentDirectors: '4人',
        businessDescription: '平安银行是中国领先的股份制商业银行之一，致力于为客户提供全方位的金融服务。银行业务涵盖公司银行、零售银行、同业银行等多个领域，拥有完善的产品体系和服务网络。',
        coreProducts: [
            { name: '个人银行业务', icon: 'fas fa-user', description: '储蓄、理财、信用卡等个人金融服务' },
            { name: '公司银行业务', icon: 'fas fa-building', description: '企业贷款、现金管理、贸易融资等' },
            { name: '投资银行业务', icon: 'fas fa-chart-line', description: '债券承销、财务顾问、资产管理等' },
            { name: '金融科技', icon: 'fas fa-laptop-code', description: '数字化银行服务和金融科技创新' }
        ],
        industryRanking: '股份制银行第2位',
        marketShare: '约8.5%',
        competitiveAdvantage: '金融科技创新、风险管控能力',
        majorCustomers: '个人客户、中小企业、大型企业',
        financialData: {
            2023: { revenue: 1876.45, profit: 378.23, assets: 52847.89, debt: 48963.12 },
            2022: { revenue: 1735.67, profit: 356.78, assets: 49234.56, debt: 45678.90 },
            2021: { revenue: 1623.45, profit: 334.56, assets: 46789.23, debt: 43456.78 }
        },
        ratios: {
            grossMargin: '65.4%',
            netMargin: '20.2%',
            roe: '12.8%',
            debtRatio: '92.6%',
            currentRatio: '1.15',
            quickRatio: '1.12',
            assetTurnover: '0.036',
            inventoryTurnover: 'N/A',
            receivableTurnover: '8.5'
        },
        businessSegments: [
            { name: '零售银行', value: 45.2, color: '#4a90e2' },
            { name: '公司银行', value: 32.8, color: '#50c878' },
            { name: '同业业务', value: 12.5, color: '#ffa500' },
            { name: '其他业务', value: 9.5, color: '#ff6b6b' }
        ]
    },
    '600036': {
        code: '600036',
        name: '招商银行',
        fullName: '招商银行股份有限公司',
        market: '上海证券交易所',
        industry: '银行业',
        establishDate: '1987-04-08',
        listingDate: '2002-04-09',
        registeredCapital: '252.20亿元',
        legalRepresentative: '缪建民',
        address: '广东省深圳市福田区深南大道7088号',
        phone: '0755-83198888',
        website: 'www.cmbchina.com',
        email: '<EMAIL>',
        postalCode: '518040',
        investorRelations: '0755-83195555',
        totalShares: '252.20亿股',
        circulatingShares: '252.20亿股',
        marketCap: '8998.32亿元',
        chairman: '缪建民',
        generalManager: '王良',
        boardSecretary: '张燕',
        cfo: '李浩',
        employeeCount: '102,123人',
        independentDirectors: '5人',
        businessDescription: '招商银行是中国领先的商业银行，以"因您而变"为服务理念，致力于为客户提供优质的金融服务。银行在零售银行、公司银行、金融市场等领域均处于行业领先地位。',
        coreProducts: [
            { name: '零售银行', icon: 'fas fa-users', description: '个人储蓄、信用卡、财富管理等' },
            { name: '公司银行', icon: 'fas fa-handshake', description: '企业融资、现金管理、投资银行等' },
            { name: '金融市场', icon: 'fas fa-chart-area', description: '资金交易、投资理财、风险管理等' },
            { name: '数字银行', icon: 'fas fa-mobile-alt', description: '手机银行、网上银行、数字化服务' }
        ],
        industryRanking: '股份制银行第1位',
        marketShare: '约12.3%',
        competitiveAdvantage: '零售银行领先、服务品质优秀',
        majorCustomers: '高净值客户、优质企业客户',
        financialData: {
            2023: { revenue: 3312.78, profit: 1398.67, assets: 98765.43, debt: 91234.56 },
            2022: { revenue: 3156.89, profit: 1281.45, assets: 89876.54, debt: 83456.78 },
            2021: { revenue: 2943.56, profit: 1199.23, assets: 82345.67, debt: 76543.21 }
        },
        ratios: {
            grossMargin: '68.9%',
            netMargin: '42.2%',
            roe: '16.5%',
            debtRatio: '92.4%',
            currentRatio: '1.18',
            quickRatio: '1.15',
            assetTurnover: '0.034',
            inventoryTurnover: 'N/A',
            receivableTurnover: '9.2'
        },
        businessSegments: [
            { name: '零售银行', value: 52.3, color: '#4a90e2' },
            { name: '公司银行', value: 28.7, color: '#50c878' },
            { name: '金融市场', value: 13.2, color: '#ffa500' },
            { name: '其他业务', value: 5.8, color: '#ff6b6b' }
        ]
    },
    '600519': {
        code: '600519',
        name: '贵州茅台',
        fullName: '贵州茅台酒股份有限公司',
        market: '上海证券交易所',
        industry: '酒、饮料和精制茶制造业',
        establishDate: '1999-11-20',
        listingDate: '2001-08-27',
        registeredCapital: '12.56亿元',
        legalRepresentative: '丁雄军',
        address: '贵州省遵义市仁怀市茅台镇',
        phone: '0851-22386002',
        website: 'www.moutai.com.cn',
        email: '<EMAIL>',
        postalCode: '564501',
        investorRelations: '0851-22386002',
        totalShares: '12.56亿股',
        circulatingShares: '12.56亿股',
        marketCap: '21,178.26亿元',
        chairman: '丁雄军',
        generalManager: '李静仁',
        boardSecretary: '樊宁屏',
        cfo: '何英姿',
        employeeCount: '26,534人',
        independentDirectors: '3人',
        businessDescription: '贵州茅台是中国白酒行业的领军企业，主要从事茅台酒及系列酒的生产和销售。公司拥有悠久的酿酒历史和独特的酿造工艺，茅台酒被誉为中国国酒，享有极高的品牌价值和市场地位。',
        coreProducts: [
            { name: '茅台酒', icon: 'fas fa-wine-bottle', description: '53度飞天茅台、五星茅台等核心产品' },
            { name: '系列酒', icon: 'fas fa-glass-cheers', description: '茅台王子酒、茅台迎宾酒等系列产品' },
            { name: '定制酒', icon: 'fas fa-gift', description: '个性化定制、纪念酒等特殊产品' },
            { name: '文创产品', icon: 'fas fa-palette', description: '茅台文化衍生品、收藏品等' }
        ],
        industryRanking: '白酒行业第1位',
        marketShare: '约18.5%',
        competitiveAdvantage: '品牌价值、稀缺性、文化底蕴',
        majorCustomers: '经销商、直销客户、电商平台',
        financialData: {
            2023: { revenue: 1374.66, profit: 627.18, assets: 2847.93, debt: 456.78 },
            2022: { revenue: 1212.34, profit: 574.89, assets: 2634.56, debt: 423.45 },
            2021: { revenue: 1094.57, profit: 524.31, assets: 2456.78, debt: 398.76 }
        },
        ratios: {
            grossMargin: '91.2%',
            netMargin: '45.6%',
            roe: '24.8%',
            debtRatio: '16.0%',
            currentRatio: '4.85',
            quickRatio: '4.23',
            assetTurnover: '0.48',
            inventoryTurnover: '1.2',
            receivableTurnover: '45.6'
        },
        businessSegments: [
            { name: '茅台酒', value: 89.2, color: '#4a90e2' },
            { name: '系列酒', value: 8.5, color: '#50c878' },
            { name: '其他业务', value: 2.3, color: '#ffa500' }
        ]
    }
};

// 全局变量
let currentCompany = null;
let revenueChart = null;
let profitChart = null;
let businessSegmentsChart = null;

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    bindEvents();
});

// 初始化页面
function initializePage() {
    // 初始化搜索
    const companySearch = document.getElementById('companySearch');
    companySearch.addEventListener('input', Utils.debounce(handleCompanySearch, 300));
    
    // 绑定回车键搜索
    companySearch.addEventListener('keypress', function(event) {
        if (event.key === 'Enter') {
            searchCompany();
        }
    });
}

// 绑定事件
function bindEvents() {
    // 点击页面其他地方隐藏搜索建议
    document.addEventListener('click', function(event) {
        const suggestions = document.getElementById('searchSuggestions');
        const searchContainer = document.querySelector('.search-container');
        
        if (!searchContainer.contains(event.target)) {
            suggestions.style.display = 'none';
        }
    });
}

// 处理公司搜索
function handleCompanySearch() {
    const searchTerm = document.getElementById('companySearch').value.toLowerCase();
    const suggestions = document.getElementById('searchSuggestions');
    
    if (!searchTerm) {
        suggestions.style.display = 'none';
        return;
    }
    
    const filteredCompanies = Object.values(companiesData).filter(company => 
        company.code.toLowerCase().includes(searchTerm) ||
        company.name.toLowerCase().includes(searchTerm) ||
        company.fullName.toLowerCase().includes(searchTerm)
    );
    
    if (filteredCompanies.length > 0) {
        suggestions.innerHTML = filteredCompanies.map(company => `
            <div class="suggestion-item" onclick="selectCompanyFromSearch('${company.code}')">
                <div>
                    <strong>${company.code}</strong> ${company.name}
                    <br><small style="color: #666;">${company.fullName}</small>
                </div>
                <div style="text-align: right;">
                    <small style="color: #666;">${company.industry}</small>
                </div>
            </div>
        `).join('');
        suggestions.style.display = 'block';
    } else {
        suggestions.style.display = 'none';
    }
}

// 从搜索结果选择公司
function selectCompanyFromSearch(companyCode) {
    document.getElementById('companySearch').value = '';
    document.getElementById('searchSuggestions').style.display = 'none';
    selectCompany(companyCode);
}

// 搜索公司
function searchCompany() {
    const searchTerm = document.getElementById('companySearch').value.trim();
    if (!searchTerm) {
        Toast.show('请输入股票代码或公司名称', 'warning');
        return;
    }
    
    // 查找公司
    const company = Object.values(companiesData).find(c => 
        c.code === searchTerm.toUpperCase() || 
        c.name === searchTerm ||
        c.fullName === searchTerm
    );
    
    if (company) {
        selectCompany(company.code);
    } else {
        showQueryFailed();
    }
}

// 选择公司
function selectCompany(companyCode) {
    const company = companiesData[companyCode];
    if (!company) {
        showQueryFailed();
        return;
    }
    
    currentCompany = company;
    showCompanyInfo(company);
    
    // 清空搜索框
    document.getElementById('companySearch').value = '';
    document.getElementById('searchSuggestions').style.display = 'none';
}

// 显示公司信息
function showCompanyInfo(company) {
    // 隐藏其他状态
    document.getElementById('noCompanySelected').style.display = 'none';
    document.getElementById('queryFailed').style.display = 'none';
    
    // 显示公司信息
    const companyInfoDisplay = document.getElementById('companyInfoDisplay');
    companyInfoDisplay.style.display = 'block';
    
    // 更新公司标题信息
    updateCompanyHeader(company);
    
    // 更新基本信息
    updateBasicInfo(company);
    
    // 更新财务数据
    updateFinancialData(company);
    
    // 更新主营业务
    updateBusinessInfo(company);
    
    // 默认显示基本信息标签页
    switchTab('basic');
}

// 显示查询失败状态
function showQueryFailed() {
    document.getElementById('noCompanySelected').style.display = 'none';
    document.getElementById('companyInfoDisplay').style.display = 'none';
    document.getElementById('queryFailed').style.display = 'block';
    
    Toast.show('未查询到该公司信息', 'error');
}

// 更新公司标题信息
function updateCompanyHeader(company) {
    document.getElementById('companyFullName').textContent = company.fullName;
    document.getElementById('companyCode').textContent = company.code;
    document.getElementById('companyMarket').textContent = company.market;
    document.getElementById('companyIndustry').textContent = company.industry;
}

// 更新基本信息
function updateBasicInfo(company) {
    // 公司概况
    document.getElementById('fullName').textContent = company.fullName;
    document.getElementById('establishDate').textContent = company.establishDate;
    document.getElementById('listingDate').textContent = company.listingDate;
    document.getElementById('registeredCapital').textContent = company.registeredCapital;
    document.getElementById('legalRepresentative').textContent = company.legalRepresentative;
    document.getElementById('industry').textContent = company.industry;
    
    // 联系信息
    document.getElementById('companyAddress').textContent = company.address;
    document.getElementById('contactPhone').textContent = company.phone;
    document.getElementById('companyWebsite').textContent = company.website;
    document.getElementById('companyEmail').textContent = company.email;
    document.getElementById('postalCode').textContent = company.postalCode;
    document.getElementById('investorRelations').textContent = company.investorRelations;
    
    // 股票信息
    document.getElementById('stockCode').textContent = company.code;
    document.getElementById('stockName').textContent = company.name;
    document.getElementById('tradingMarket').textContent = company.market;
    document.getElementById('totalShares').textContent = company.totalShares;
    document.getElementById('circulatingShares').textContent = company.circulatingShares;
    document.getElementById('marketCap').textContent = company.marketCap;
    
    // 管理层信息
    document.getElementById('chairman').textContent = company.chairman;
    document.getElementById('generalManager').textContent = company.generalManager;
    document.getElementById('boardSecretary').textContent = company.boardSecretary;
    document.getElementById('cfo').textContent = company.cfo;
    document.getElementById('employeeCount').textContent = company.employeeCount;
    document.getElementById('independentDirectors').textContent = company.independentDirectors;
}

// 更新财务数据
function updateFinancialData(company) {
    // 更新财务表格
    const financialTableBody = document.getElementById('financialTableBody');
    const financialData = company.financialData;

    const financialItems = [
        { label: '营业收入(亿元)', key: 'revenue' },
        { label: '净利润(亿元)', key: 'profit' },
        { label: '总资产(亿元)', key: 'assets' },
        { label: '总负债(亿元)', key: 'debt' }
    ];

    financialTableBody.innerHTML = financialItems.map(item => {
        const data2023 = financialData[2023][item.key];
        const data2022 = financialData[2022][item.key];
        const data2021 = financialData[2021][item.key];
        const growth = ((data2023 - data2022) / data2022 * 100).toFixed(1);
        const growthClass = growth >= 0 ? 'price-up' : 'price-down';
        const growthSymbol = growth >= 0 ? '+' : '';

        return `
            <tr>
                <td>${item.label}</td>
                <td>${data2023.toFixed(2)}</td>
                <td>${data2022.toFixed(2)}</td>
                <td>${data2021.toFixed(2)}</td>
                <td class="${growthClass}">${growthSymbol}${growth}%</td>
            </tr>
        `;
    }).join('');

    // 更新财务比率
    const ratios = company.ratios;
    document.getElementById('grossMargin').textContent = ratios.grossMargin;
    document.getElementById('netMargin').textContent = ratios.netMargin;
    document.getElementById('roe').textContent = ratios.roe;
    document.getElementById('debtRatio').textContent = ratios.debtRatio;
    document.getElementById('currentRatio').textContent = ratios.currentRatio;
    document.getElementById('quickRatio').textContent = ratios.quickRatio;
    document.getElementById('assetTurnover').textContent = ratios.assetTurnover;
    document.getElementById('inventoryTurnover').textContent = ratios.inventoryTurnover;
    document.getElementById('receivableTurnover').textContent = ratios.receivableTurnover;

    // 更新图表
    updateFinancialCharts(company);
}

// 更新财务图表
function updateFinancialCharts(company) {
    const financialData = company.financialData;
    const years = ['2021', '2022', '2023'];

    // 营业收入图表
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    if (revenueChart) {
        revenueChart.destroy();
    }

    revenueChart = new Chart(revenueCtx, {
        type: 'bar',
        data: {
            labels: years,
            datasets: [{
                label: '营业收入(亿元)',
                data: years.map(year => financialData[year].revenue),
                backgroundColor: 'rgba(74, 144, 226, 0.8)',
                borderColor: 'rgba(74, 144, 226, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value + '亿';
                        }
                    }
                }
            }
        }
    });

    // 净利润图表
    const profitCtx = document.getElementById('profitChart').getContext('2d');
    if (profitChart) {
        profitChart.destroy();
    }

    profitChart = new Chart(profitCtx, {
        type: 'line',
        data: {
            labels: years,
            datasets: [{
                label: '净利润(亿元)',
                data: years.map(year => financialData[year].profit),
                borderColor: 'rgba(80, 200, 120, 1)',
                backgroundColor: 'rgba(80, 200, 120, 0.2)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value + '亿';
                        }
                    }
                }
            }
        }
    });
}

// 更新主营业务信息
function updateBusinessInfo(company) {
    // 业务描述
    document.getElementById('businessDescription').textContent = company.businessDescription;

    // 核心产品
    const coreProducts = document.getElementById('coreProducts');
    coreProducts.innerHTML = company.coreProducts.map(product => `
        <div class="product-item">
            <div class="product-icon">
                <i class="${product.icon}"></i>
            </div>
            <div class="product-name">${product.name}</div>
            <div class="product-description">${product.description}</div>
        </div>
    `).join('');

    // 市场地位
    document.getElementById('industryRanking').textContent = company.industryRanking;
    document.getElementById('marketShare').textContent = company.marketShare;
    document.getElementById('competitiveAdvantage').textContent = company.competitiveAdvantage;
    document.getElementById('majorCustomers').textContent = company.majorCustomers;

    // 业务分部图表
    updateBusinessSegmentsChart(company);
}

// 更新业务分部图表
function updateBusinessSegmentsChart(company) {
    const ctx = document.getElementById('businessSegmentsChart').getContext('2d');
    if (businessSegmentsChart) {
        businessSegmentsChart.destroy();
    }

    const segments = company.businessSegments;

    businessSegmentsChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: segments.map(s => s.name),
            datasets: [{
                data: segments.map(s => s.value),
                backgroundColor: segments.map(s => s.color),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ' + context.parsed + '%';
                        }
                    }
                }
            }
        }
    });

    // 更新图例
    const segmentsLegend = document.getElementById('segmentsLegend');
    segmentsLegend.innerHTML = segments.map(segment => `
        <div class="legend-item">
            <div class="legend-color" style="background-color: ${segment.color};"></div>
            <div class="legend-label">${segment.name}</div>
            <div class="legend-value">${segment.value}%</div>
        </div>
    `).join('');
}

// 切换标签页
function switchTab(tabName) {
    // 更新标签状态
    document.querySelectorAll('.info-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // 更新内容显示
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`${tabName}-content`).classList.add('active');

    // 如果切换到财务数据标签页，重新渲染图表
    if (tabName === 'financial' && currentCompany) {
        setTimeout(() => {
            updateFinancialCharts(currentCompany);
        }, 100);
    }

    // 如果切换到主营业务标签页，重新渲染业务分部图表
    if (tabName === 'business' && currentCompany) {
        setTimeout(() => {
            updateBusinessSegmentsChart(currentCompany);
        }, 100);
    }
}

// 清空搜索
function clearSearch() {
    document.getElementById('companySearch').value = '';
    document.getElementById('searchSuggestions').style.display = 'none';

    // 显示默认状态
    document.getElementById('companyInfoDisplay').style.display = 'none';
    document.getElementById('queryFailed').style.display = 'none';
    document.getElementById('noCompanySelected').style.display = 'block';

    currentCompany = null;

    // 销毁图表
    if (revenueChart) {
        revenueChart.destroy();
        revenueChart = null;
    }
    if (profitChart) {
        profitChart.destroy();
        profitChart = null;
    }
    if (businessSegmentsChart) {
        businessSegmentsChart.destroy();
        businessSegmentsChart = null;
    }

    Toast.show('已清空搜索结果', 'info');
}

// 刷新数据
function refreshData() {
    if (!currentCompany) {
        Toast.show('请先选择要刷新的公司', 'warning');
        return;
    }

    // 模拟数据刷新
    Toast.show('正在刷新数据...', 'info');

    setTimeout(() => {
        // 模拟更新财务数据
        const company = companiesData[currentCompany.code];
        if (company) {
            // 随机更新一些数据
            const currentYear = 2023;
            const variation = (Math.random() - 0.5) * 0.1; // ±5%的随机变化

            company.financialData[currentYear].revenue *= (1 + variation);
            company.financialData[currentYear].profit *= (1 + variation);

            // 重新显示公司信息
            showCompanyInfo(company);

            Toast.show('数据刷新完成', 'success');
        }
    }, 1500);
}

// 下载PDF报告
function downloadPDFReport() {
    if (!currentCompany) {
        Toast.show('请先选择公司', 'warning');
        return;
    }

    Toast.show('正在生成PDF报告...', 'info');

    // 模拟PDF生成和下载
    setTimeout(() => {
        // 创建模拟的PDF内容
        const pdfContent = generatePDFContent(currentCompany);

        // 创建Blob并下载
        const blob = new Blob([pdfContent], { type: 'text/plain;charset=utf-8' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `${currentCompany.name}_公司信息报告_${Utils.formatDate(new Date(), 'YYYY-MM-DD')}.txt`;
        link.click();

        Toast.show('PDF报告已生成并下载', 'success');
    }, 2000);
}

// 生成PDF内容
function generatePDFContent(company) {
    return `
公司基本信息报告
================

公司名称：${company.fullName}
股票代码：${company.code}
所属行业：${company.industry}
生成时间：${new Date().toLocaleString()}

一、基本信息
-----------
公司全称：${company.fullName}
成立日期：${company.establishDate}
上市日期：${company.listingDate}
注册资本：${company.registeredCapital}
法定代表人：${company.legalRepresentative}
公司地址：${company.address}
联系电话：${company.phone}

二、财务数据（近三年）
------------------
2023年营业收入：${company.financialData[2023].revenue.toFixed(2)}亿元
2023年净利润：${company.financialData[2023].profit.toFixed(2)}亿元
2022年营业收入：${company.financialData[2022].revenue.toFixed(2)}亿元
2022年净利润：${company.financialData[2022].profit.toFixed(2)}亿元
2021年营业收入：${company.financialData[2021].revenue.toFixed(2)}亿元
2021年净利润：${company.financialData[2021].profit.toFixed(2)}亿元

三、主营业务
-----------
业务描述：${company.businessDescription}
行业排名：${company.industryRanking}
市场份额：${company.marketShare}
竞争优势：${company.competitiveAdvantage}

四、核心产品
-----------
${company.coreProducts.map(product => `${product.name}：${product.description}`).join('\n')}

报告生成完成。
    `.trim();
}
