<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色分配管理 - 金融软件项目管理系统</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/role-assignment.css">
    <link rel="stylesheet" href="css/macaron-theme.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body data-page="role-assignment">
    <div class="container">
        <header class="header">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                <h1>金融软件项目管理系统</h1>
            </div>
            <nav class="nav">
                <a href="index.html" class="btn btn-secondary">
                    <i class="fas fa-home"></i> 返回首页
                </a>
                <div class="user-info">
                    <span>欢迎，管理员</span>
                    <i class="fas fa-user-circle"></i>
                </div>
            </nav>
        </header>

        <main class="main">
            <div class="page-header">
                <h2><i class="fas fa-user-tag"></i> 角色分配管理</h2>
                <p>为用户分配相应的系统角色</p>
            </div>

            <div class="content-layout">
                <!-- 左侧用户列表 -->
                <div class="user-list-panel">
                    <div class="panel-header">
                        <h3><i class="fas fa-users"></i> 用户列表</h3>
                        <div class="panel-actions">
                            <button class="btn btn-secondary" onclick="refreshUsers()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                            <button class="btn btn-secondary" onclick="exportUsers()">
                                <i class="fas fa-download"></i> 导出
                            </button>
                        </div>
                    </div>

                    <div class="search-filter-section">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="userSearch" placeholder="搜索用户..." class="form-control">
                        </div>
                        <div class="filter-row">
                            <select class="form-control form-select" id="departmentFilter">
                                <option value="">全部部门</option>
                                <option value="技术部">技术部</option>
                                <option value="业务部">业务部</option>
                                <option value="风控部">风控部</option>
                                <option value="客服部">客服部</option>
                            </select>
                            <select class="form-control form-select" id="roleFilter">
                                <option value="">全部角色</option>
                                <option value="系统管理员">系统管理员</option>
                                <option value="普通用户">普通用户</option>
                                <option value="数据分析师">数据分析师</option>
                                <option value="交易员">交易员</option>
                            </select>
                        </div>
                    </div>

                    <div class="user-table-container">
                        <table class="table" id="usersTable">
                            <thead>
                                <tr>
                                    <th>用户ID</th>
                                    <th>用户名</th>
                                    <th>所属部门</th>
                                    <th>当前角色</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                                <!-- 用户数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>

                        <div class="table-pagination">
                            <div class="pagination-info">
                                <span id="userPageInfo">显示 1-10 条，共 50 条</span>
                            </div>
                            <div class="pagination-controls">
                                <button class="btn btn-secondary" id="userPrevPage" onclick="prevUserPage()">
                                    <i class="fas fa-chevron-left"></i> 上一页
                                </button>
                                <div class="page-numbers" id="userPageNumbers">
                                    <!-- 页码将动态生成 -->
                                </div>
                                <button class="btn btn-secondary" id="userNextPage" onclick="nextUserPage()">
                                    下一页 <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧角色分配区 -->
                <div class="role-assignment-panel">
                    <div class="panel-header">
                        <h3><i class="fas fa-user-cog"></i> 角色分配区</h3>
                        <div class="panel-actions">
                            <button class="btn btn-success" onclick="saveAssignment()" id="saveBtn" disabled>
                                <i class="fas fa-save"></i> 保存分配
                            </button>
                            <button class="btn btn-secondary" onclick="resetAssignment()" id="resetBtn" disabled>
                                <i class="fas fa-undo"></i> 重置
                            </button>
                        </div>
                    </div>

                    <div class="assignment-content" id="assignmentContent">
                        <div class="welcome-message">
                            <i class="fas fa-hand-point-left"></i>
                            <h4>选择用户进行角色分配</h4>
                            <p>请从左侧用户列表中选择一个用户来分配角色</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 批量分配面板 -->
            <div class="batch-assignment-panel">
                <div class="panel-header">
                    <h3><i class="fas fa-users-cog"></i> 批量角色分配</h3>
                    <div class="selected-users-count">
                        已选择 <span id="selectedUsersCount">0</span> 个用户
                    </div>
                </div>
                <div class="batch-content">
                    <div class="batch-role-selection">
                        <label class="form-label">选择要分配的角色：</label>
                        <div class="role-checkboxes" id="batchRoleCheckboxes">
                            <!-- 角色复选框将动态生成 -->
                        </div>
                    </div>
                    <div class="batch-actions">
                        <button class="btn btn-primary" onclick="batchAssignRoles()" id="batchAssignBtn" disabled>
                            <i class="fas fa-users"></i> 批量分配
                        </button>
                        <button class="btn btn-danger" onclick="batchRemoveRoles()" id="batchRemoveBtn" disabled>
                            <i class="fas fa-user-minus"></i> 批量移除
                        </button>
                        <button class="btn btn-secondary" onclick="clearUserSelection()">
                            <i class="fas fa-times"></i> 清除选择
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 角色分配历史模态框 -->
    <div class="modal" id="assignmentHistoryModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">角色分配历史</h3>
                <button class="modal-close" onclick="hideAssignmentHistory()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="history-content" id="historyContent">
                    <!-- 历史记录将动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideAssignmentHistory()">关闭</button>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script src="js/role-assignment.js"></script>
</body>
</html>
