// 股票实时交易模拟页面JavaScript

// 模拟股票数据
let stocksData = [
    {
        code: '000001',
        name: '平安银行',
        market: 'sz',
        currentPrice: 12.85,
        openPrice: 12.60,
        highPrice: 13.20,
        lowPrice: 12.45,
        prevClose: 12.70,
        volume: 125680000,
        turnover: 1612345000,
        change: 0.15,
        changePercent: 1.18
    },
    {
        code: '000002',
        name: '万科A',
        market: 'sz',
        currentPrice: 8.95,
        openPrice: 9.10,
        highPrice: 9.25,
        lowPrice: 8.88,
        prevClose: 9.05,
        volume: 89560000,
        turnover: 805234000,
        change: -0.10,
        changePercent: -1.10
    },
    {
        code: '600036',
        name: '招商银行',
        market: 'sh',
        currentPrice: 35.68,
        openPrice: 35.20,
        highPrice: 36.15,
        lowPrice: 35.05,
        prevClose: 35.45,
        volume: 45230000,
        turnover: 1612890000,
        change: 0.23,
        changePercent: 0.65
    },
    {
        code: '600519',
        name: '贵州茅台',
        market: 'sh',
        currentPrice: 1685.50,
        openPrice: 1678.00,
        highPrice: 1698.80,
        lowPrice: 1672.30,
        prevClose: 1680.20,
        volume: 1250000,
        turnover: 2108750000,
        change: 5.30,
        changePercent: 0.32
    },
    {
        code: '000858',
        name: '五粮液',
        market: 'sz',
        currentPrice: 128.45,
        openPrice: 126.80,
        highPrice: 130.20,
        lowPrice: 125.90,
        prevClose: 127.30,
        volume: 8950000,
        turnover: 1148230000,
        change: 1.15,
        changePercent: 0.90
    }
];

// 模拟持仓数据
let portfolioData = [
    {
        code: '000001',
        name: '平安银行',
        quantity: 10000,
        costPrice: 12.20,
        currentPrice: 12.85,
        profit: 6500,
        profitPercent: 5.33
    },
    {
        code: '600036',
        name: '招商银行',
        quantity: 5000,
        costPrice: 34.80,
        currentPrice: 35.68,
        profit: 4400,
        profitPercent: 2.53
    },
    {
        code: '000858',
        name: '五粮液',
        quantity: 1000,
        costPrice: 130.50,
        currentPrice: 128.45,
        profit: -2050,
        profitPercent: -1.57
    }
];

// 模拟成交明细数据
let tradesData = [
    { time: '14:30:25', price: 12.85, volume: 1200, direction: 'buy', amount: 15420 },
    { time: '14:30:22', price: 12.84, volume: 800, direction: 'sell', amount: 10272 },
    { time: '14:30:19', price: 12.86, volume: 1500, direction: 'buy', amount: 19290 },
    { time: '14:30:16', price: 12.83, volume: 600, direction: 'sell', amount: 7698 },
    { time: '14:30:13', price: 12.85, volume: 2000, direction: 'buy', amount: 25700 }
];

// 模拟资讯数据
let newsData = [
    {
        title: '平安银行发布2024年第一季度业绩报告',
        summary: '平安银行今日发布2024年第一季度业绩报告，净利润同比增长8.5%，资产质量持续改善...',
        time: '2024-01-20 14:00:00',
        source: '财经网'
    },
    {
        title: '央行宣布降准0.5个百分点',
        summary: '中国人民银行今日宣布，为支持实体经济发展，决定于1月25日下调存款准备金率0.5个百分点...',
        time: '2024-01-20 13:30:00',
        source: '新华财经'
    },
    {
        title: '银行板块午后拉升，多只个股涨停',
        summary: '银行板块午后持续拉升，平安银行、招商银行等多只个股涨幅超过5%，市场情绪回暖...',
        time: '2024-01-20 13:15:00',
        source: '证券时报'
    }
];

// 全局变量
let selectedStock = null;
let refreshInterval = 10000; // 10秒
let refreshTimer = null;
let isRefreshPaused = false;
let stockChart = null;
let currentChartType = 'realtime';

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    bindEvents();
    renderMarketTable();
    renderTradesTable();
    renderNewsList();
    renderPortfolioTable();
    startAutoRefresh();
    updateMarketTime();
    
    // 默认选择第一只股票
    selectStock('000001');
});

// 初始化页面
function initializePage() {
    // 初始化搜索
    const stockSearch = document.getElementById('stockSearch');
    stockSearch.addEventListener('input', Utils.debounce(handleStockSearch, 300));
    
    // 初始化刷新间隔
    const refreshIntervalSelect = document.getElementById('refreshInterval');
    refreshIntervalSelect.addEventListener('change', handleRefreshIntervalChange);
    
    // 初始化市场筛选
    const marketFilter = document.getElementById('marketFilter');
    marketFilter.addEventListener('change', handleMarketFilter);
    
    // 初始化图表
    initializeChart();
}

// 绑定事件
function bindEvents() {
    // 可以在这里添加其他事件绑定
}

// 处理股票搜索
function handleStockSearch() {
    const searchTerm = document.getElementById('stockSearch').value.toLowerCase();
    const suggestions = document.getElementById('searchSuggestions');
    
    if (!searchTerm) {
        suggestions.style.display = 'none';
        return;
    }
    
    const filteredStocks = stocksData.filter(stock => 
        stock.code.toLowerCase().includes(searchTerm) ||
        stock.name.toLowerCase().includes(searchTerm)
    );
    
    if (filteredStocks.length > 0) {
        suggestions.innerHTML = filteredStocks.map(stock => `
            <div class="suggestion-item" onclick="selectStockFromSearch('${stock.code}')">
                <strong>${stock.code}</strong> ${stock.name}
                <span style="float: right; color: ${stock.change >= 0 ? '#dc3545' : '#28a745'};">
                    ${stock.currentPrice.toFixed(2)}
                </span>
            </div>
        `).join('');
        suggestions.style.display = 'block';
    } else {
        suggestions.style.display = 'none';
    }
}

// 从搜索结果选择股票
function selectStockFromSearch(stockCode) {
    document.getElementById('stockSearch').value = '';
    document.getElementById('searchSuggestions').style.display = 'none';
    selectStock(stockCode);
}

// 搜索股票
function searchStock() {
    const searchTerm = document.getElementById('stockSearch').value;
    if (searchTerm) {
        const stock = stocksData.find(s => 
            s.code === searchTerm || s.name === searchTerm
        );
        if (stock) {
            selectStock(stock.code);
        } else {
            Toast.show('未找到相关股票', 'warning');
        }
    }
}

// 选择股票
function selectStock(stockCode) {
    const stock = stocksData.find(s => s.code === stockCode);
    if (!stock) return;
    
    selectedStock = stock;
    updateStockInfo(stock);
    updateOrderbook(stock);
    updateChart(stock);
    highlightSelectedStock(stockCode);
    
    // 更新成交明细（模拟该股票的成交数据）
    generateTradesData(stock);
    renderTradesTable();
}

// 更新股票信息
function updateStockInfo(stock) {
    const stockInfo = document.getElementById('stockInfo');
    const changeClass = stock.change >= 0 ? 'price-up' : 'price-down';
    const changeSymbol = stock.change >= 0 ? '+' : '';
    
    stockInfo.innerHTML = `
        <div class="stock-name">${stock.code} ${stock.name}</div>
        <div class="stock-price ${changeClass}">${stock.currentPrice.toFixed(2)}</div>
        <div class="stock-change ${changeClass}">
            ${changeSymbol}${stock.change.toFixed(2)} (${changeSymbol}${stock.changePercent.toFixed(2)}%)
        </div>
    `;
}

// 更新买五卖五盘口
function updateOrderbook(stock) {
    const currentPrice = stock.currentPrice;
    const sellLevels = document.getElementById('sellLevels');
    const buyLevels = document.getElementById('buyLevels');
    const currentPriceElement = document.getElementById('currentPrice');
    const spreadElement = document.getElementById('spread');
    
    // 生成模拟的买五卖五数据
    const sellOrders = [];
    const buyOrders = [];
    
    for (let i = 1; i <= 5; i++) {
        sellOrders.push({
            price: (currentPrice + i * 0.01).toFixed(2),
            volume: Math.floor(Math.random() * 10000) + 1000
        });
        buyOrders.push({
            price: (currentPrice - i * 0.01).toFixed(2),
            volume: Math.floor(Math.random() * 10000) + 1000
        });
    }
    
    sellLevels.innerHTML = sellOrders.reverse().map((order, index) => `
        <div class="orderbook-level">
            <span>卖${5-index}</span>
            <span>${order.price}</span>
            <span>${order.volume}</span>
        </div>
    `).join('');
    
    buyLevels.innerHTML = buyOrders.map((order, index) => `
        <div class="orderbook-level">
            <span>买${index+1}</span>
            <span>${order.price}</span>
            <span>${order.volume}</span>
        </div>
    `).join('');
    
    // 更新当前价格
    const changeClass = stock.change >= 0 ? 'price-up' : 'price-down';
    const changeSymbol = stock.change >= 0 ? '+' : '';
    
    currentPriceElement.innerHTML = `
        <div class="price-value ${changeClass}">${currentPrice.toFixed(2)}</div>
        <div class="price-change ${changeClass}">
            ${changeSymbol}${stock.change.toFixed(2)} (${changeSymbol}${stock.changePercent.toFixed(2)}%)
        </div>
    `;
    
    // 更新价差
    const spread = (sellOrders[4].price - buyOrders[0].price).toFixed(2);
    spreadElement.textContent = spread;
    
    // 更新时间
    document.getElementById('orderbookTime').textContent = new Date().toLocaleTimeString();
}

// 初始化图表
function initializeChart() {
    const ctx = document.getElementById('stockChart').getContext('2d');
    
    stockChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '价格',
                data: [],
                borderColor: '#4a90e2',
                backgroundColor: 'rgba(74, 144, 226, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    display: true,
                    grid: {
                        color: '#f0f0f0'
                    }
                },
                y: {
                    display: true,
                    grid: {
                        color: '#f0f0f0'
                    },
                    ticks: {
                        callback: function(value) {
                            return value.toFixed(2);
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

// 更新图表
function updateChart(stock) {
    if (!stockChart) return;
    
    // 生成模拟的时间序列数据
    const now = new Date();
    const labels = [];
    const data = [];
    
    for (let i = 60; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 60000); // 每分钟一个数据点
        labels.push(time.toLocaleTimeString().slice(0, 5));
        
        // 生成围绕当前价格波动的模拟数据
        const basePrice = stock.currentPrice;
        const variation = (Math.random() - 0.5) * 0.5; // ±0.25的随机波动
        data.push(basePrice + variation);
    }
    
    stockChart.data.labels = labels;
    stockChart.data.datasets[0].data = data;
    stockChart.data.datasets[0].label = `${stock.code} ${stock.name}`;
    
    // 根据涨跌设置颜色
    const color = stock.change >= 0 ? '#dc3545' : '#28a745';
    stockChart.data.datasets[0].borderColor = color;
    stockChart.data.datasets[0].backgroundColor = color + '20';
    
    stockChart.update();
}

// 切换图表类型
function switchChartType(type) {
    currentChartType = type;
    
    // 更新标签页状态
    document.querySelectorAll('.chart-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[data-type="${type}"]`).classList.add('active');
    
    // 更新图表（这里可以根据不同类型加载不同的数据）
    if (selectedStock) {
        updateChart(selectedStock);
    }
    
    Toast.show(`已切换到${getChartTypeName(type)}`, 'info');
}

// 获取图表类型名称
function getChartTypeName(type) {
    const names = {
        'realtime': '分时图',
        'daily': '日K线',
        'weekly': '周K线',
        'monthly': '月K线'
    };
    return names[type] || '分时图';
}

// 渲染市场表格
function renderMarketTable() {
    const tbody = document.getElementById('marketTableBody');
    const marketFilter = document.getElementById('marketFilter').value;
    
    let filteredStocks = stocksData;
    if (marketFilter !== 'all') {
        filteredStocks = stocksData.filter(stock => stock.market === marketFilter);
    }
    
    tbody.innerHTML = filteredStocks.map(stock => {
        const changeClass = stock.change >= 0 ? 'price-up' : 'price-down';
        const changeSymbol = stock.change >= 0 ? '+' : '';
        const isSelected = selectedStock && selectedStock.code === stock.code;
        
        return `
            <tr class="${isSelected ? 'selected' : ''}" onclick="selectStock('${stock.code}')">
                <td>${stock.code}</td>
                <td>${stock.name}</td>
                <td class="${changeClass}">${stock.currentPrice.toFixed(2)}</td>
                <td class="${changeClass}">${changeSymbol}${stock.change.toFixed(2)}</td>
                <td class="${changeClass}">${changeSymbol}${stock.changePercent.toFixed(2)}%</td>
                <td>${Utils.formatNumber(stock.volume / 10000, 0)}万</td>
                <td>${Utils.formatNumber(stock.turnover / 100000000, 2)}亿</td>
                <td>${stock.openPrice.toFixed(2)}</td>
                <td class="price-up">${stock.highPrice.toFixed(2)}</td>
                <td class="price-down">${stock.lowPrice.toFixed(2)}</td>
            </tr>
        `;
    }).join('');
}

// 高亮选中的股票
function highlightSelectedStock(stockCode) {
    document.querySelectorAll('#marketTableBody tr').forEach(row => {
        row.classList.remove('selected');
    });
    
    const selectedRow = document.querySelector(`#marketTableBody tr[onclick="selectStock('${stockCode}')"]`);
    if (selectedRow) {
        selectedRow.classList.add('selected');
    }
}

// 生成成交明细数据
function generateTradesData(stock) {
    tradesData = [];
    const now = new Date();
    
    for (let i = 0; i < 20; i++) {
        const time = new Date(now.getTime() - i * 3000); // 每3秒一笔成交
        const price = stock.currentPrice + (Math.random() - 0.5) * 0.1;
        const volume = Math.floor(Math.random() * 2000) + 100;
        const direction = Math.random() > 0.5 ? 'buy' : 'sell';
        
        tradesData.unshift({
            time: time.toLocaleTimeString(),
            price: price.toFixed(2),
            volume: volume,
            direction: direction,
            amount: Math.floor(price * volume)
        });
    }
}

// 渲染成交明细表格
function renderTradesTable() {
    const tbody = document.getElementById('tradesTableBody');
    
    tbody.innerHTML = tradesData.slice(0, 15).map(trade => {
        const directionClass = trade.direction === 'buy' ? 'price-up' : 'price-down';
        const directionText = trade.direction === 'buy' ? '买入' : '卖出';
        
        return `
            <tr>
                <td>${trade.time}</td>
                <td class="${directionClass}">${trade.price}</td>
                <td>${trade.volume}</td>
                <td class="${directionClass}">${directionText}</td>
                <td>${Utils.formatNumber(trade.amount)}</td>
            </tr>
        `;
    }).join('');
}

// 渲染资讯列表
function renderNewsList() {
    const newsList = document.getElementById('newsList');
    
    newsList.innerHTML = newsData.map(news => `
        <div class="news-item">
            <div class="news-title">${news.title}</div>
            <div class="news-summary">${news.summary}</div>
            <div class="news-meta">
                <span>${news.source}</span>
                <span>${news.time}</span>
            </div>
        </div>
    `).join('');
}

// 渲染持仓表格
function renderPortfolioTable() {
    const tbody = document.getElementById('portfolioTableBody');
    
    tbody.innerHTML = portfolioData.map(holding => {
        const profitClass = holding.profit >= 0 ? 'price-up' : 'price-down';
        const profitSymbol = holding.profit >= 0 ? '+' : '';
        
        return `
            <tr>
                <td>${holding.code}</td>
                <td>${holding.name}</td>
                <td>${holding.quantity}</td>
                <td>${holding.costPrice.toFixed(2)}</td>
                <td>${holding.currentPrice.toFixed(2)}</td>
                <td class="${profitClass}">${profitSymbol}${Utils.formatNumber(holding.profit)}</td>
                <td class="${profitClass}">${profitSymbol}${holding.profitPercent.toFixed(2)}%</td>
            </tr>
        `;
    }).join('');
}

// 切换信息标签页
function switchInfoTab(tabName) {
    // 更新标签状态
    document.querySelectorAll('.info-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    
    // 更新内容显示
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`${tabName}-content`).classList.add('active');
}

// 处理刷新间隔变化
function handleRefreshIntervalChange() {
    const newInterval = parseInt(document.getElementById('refreshInterval').value) * 1000;
    refreshInterval = newInterval;
    
    if (!isRefreshPaused) {
        stopAutoRefresh();
        startAutoRefresh();
    }
    
    Toast.show(`刷新间隔已设置为${newInterval/1000}秒`, 'info');
}

// 处理市场筛选
function handleMarketFilter() {
    renderMarketTable();
}

// 切换刷新状态
function toggleRefresh() {
    const pauseBtn = document.getElementById('pauseRefresh');
    
    if (isRefreshPaused) {
        startAutoRefresh();
        pauseBtn.innerHTML = '<i class="fas fa-pause"></i> 暂停';
        isRefreshPaused = false;
        Toast.show('已恢复自动刷新', 'info');
    } else {
        stopAutoRefresh();
        pauseBtn.innerHTML = '<i class="fas fa-play"></i> 继续';
        isRefreshPaused = true;
        Toast.show('已暂停自动刷新', 'info');
    }
}

// 开始自动刷新
function startAutoRefresh() {
    refreshTimer = setInterval(() => {
        updateMarketData();
        updateMarketTime();
        renderMarketTable();
        
        if (selectedStock) {
            updateOrderbook(selectedStock);
            generateTradesData(selectedStock);
            renderTradesTable();
        }
    }, refreshInterval);
}

// 停止自动刷新
function stopAutoRefresh() {
    if (refreshTimer) {
        clearInterval(refreshTimer);
        refreshTimer = null;
    }
}

// 更新市场数据（模拟实时数据变化）
function updateMarketData() {
    stocksData.forEach(stock => {
        // 模拟价格波动
        const variation = (Math.random() - 0.5) * 0.02; // ±1%的随机波动
        const newPrice = Math.max(0.01, stock.currentPrice * (1 + variation));
        
        stock.currentPrice = parseFloat(newPrice.toFixed(2));
        stock.change = stock.currentPrice - stock.prevClose;
        stock.changePercent = (stock.change / stock.prevClose) * 100;
        
        // 更新最高最低价
        stock.highPrice = Math.max(stock.highPrice, stock.currentPrice);
        stock.lowPrice = Math.min(stock.lowPrice, stock.currentPrice);
        
        // 更新成交量和成交额
        stock.volume += Math.floor(Math.random() * 100000);
        stock.turnover = stock.volume * stock.currentPrice;
    });
    
    // 更新持仓数据
    portfolioData.forEach(holding => {
        const stock = stocksData.find(s => s.code === holding.code);
        if (stock) {
            holding.currentPrice = stock.currentPrice;
            holding.profit = (holding.currentPrice - holding.costPrice) * holding.quantity;
            holding.profitPercent = ((holding.currentPrice - holding.costPrice) / holding.costPrice) * 100;
        }
    });
}

// 更新市场时间
function updateMarketTime() {
    const now = new Date();
    document.getElementById('marketTime').textContent = now.toLocaleString();
}

// 全屏显示图表
function fullscreenChart() {
    const chartContainer = document.querySelector('.chart-container');
    if (chartContainer.requestFullscreen) {
        chartContainer.requestFullscreen();
    } else if (chartContainer.webkitRequestFullscreen) {
        chartContainer.webkitRequestFullscreen();
    } else if (chartContainer.msRequestFullscreen) {
        chartContainer.msRequestFullscreen();
    }
}

// 保存图表
function saveChart() {
    if (stockChart) {
        const url = stockChart.toBase64Image();
        const link = document.createElement('a');
        link.download = `${selectedStock ? selectedStock.code : 'chart'}_${Utils.formatDate(new Date(), 'YYYY-MM-DD_HH-mm-ss')}.png`;
        link.href = url;
        link.click();
        
        Toast.show('图表已保存', 'success');
    }
}

// 页面卸载时清理定时器
window.addEventListener('beforeunload', function() {
    stopAutoRefresh();
});
