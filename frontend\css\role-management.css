/* 角色管理页面样式 */
.page-header {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(6px);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    text-align: center;
}

.page-header h2 {
    color: #8e8e93;
    font-size: 1.3rem;
    margin-bottom: 0.3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    font-weight: 500;
}

.page-header h2 i {
    color: #f8c291;
}

.page-header p {
    color: #a8a8a8;
    font-size: 11px;
}

/* 内容布局 */
.content-layout {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

/* 左侧边栏 */
.sidebar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1.5rem;
    height: fit-content;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 2rem;
}

.operation-panel h3,
.operation-panel h4 {
    color: #2c3e50;
    font-size: 1.1rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.operation-panel h3 i,
.operation-panel h4 i {
    color: #4a90e2;
}

.operation-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 2rem;
}

.operation-buttons .btn {
    justify-content: flex-start;
    width: 100%;
}

/* 搜索区域 */
.search-section {
    margin-bottom: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

/* 统计信息 */
.stats-section {
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}

.stat-item {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    border: 1px solid #e9ecef;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #4a90e2;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.8rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 主要内容区域 */
.main-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-header {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-header h3 {
    margin: 0;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.table-actions {
    display: flex;
    gap: 0.75rem;
}

.table-actions .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
}

.table-actions .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

/* 表格样式增强 */
.table {
    margin: 0;
}

.table th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    font-weight: 600;
    color: #495057;
    padding: 1rem;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
}

/* 状态徽章 */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.enabled {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.disabled {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-buttons .btn {
    padding: 0.5rem;
    font-size: 0.8rem;
    min-width: auto;
}

/* 分页样式 */
.table-pagination {
    background: #f8f9fa;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #dee2e6;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-numbers {
    display: flex;
    gap: 0.25rem;
}

.page-number {
    padding: 0.5rem 0.75rem;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.page-number:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.page-number.active {
    background: #4a90e2;
    border-color: #4a90e2;
    color: white;
}

/* 模态框增强 */
.modal-content {
    max-width: 600px;
}

.required {
    color: #dc3545;
}

.delete-warning {
    text-align: center;
    padding: 1rem;
}

.delete-warning i {
    font-size: 3rem;
    color: #ffc107;
    margin-bottom: 1rem;
}

.delete-warning p {
    margin-bottom: 0.5rem;
    color: #666;
}

.delete-info {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 0.75rem;
    margin-top: 1rem;
}

/* 复选框样式 */
input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #4a90e2;
    cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .content-layout {
        grid-template-columns: 250px 1fr;
        gap: 1.5rem;
    }
    
    .sidebar {
        padding: 1rem;
    }
}

@media (max-width: 768px) {
    .content-layout {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .sidebar {
        position: static;
        order: 2;
    }
    
    .main-content {
        order: 1;
    }
    
    .table-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .table-pagination {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .operation-buttons {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .operation-buttons .btn {
        flex: 1;
        min-width: 120px;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 1.5rem 1rem;
    }
    
    .page-header h2 {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    .table {
        min-width: 600px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* 动画效果 */
.table tbody tr {
    transition: all 0.3s ease;
}

.stat-item {
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.table-container {
    position: relative;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.empty-state i {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 1rem;
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: #999;
}

.empty-state p {
    margin-bottom: 1.5rem;
}
