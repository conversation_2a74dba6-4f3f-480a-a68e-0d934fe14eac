/* 权限分配管理页面样式 */
.page-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.page-header h2 {
    color: #2c3e50;
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.page-header h2 i {
    color: #4a90e2;
}

.page-header p {
    color: #666;
    font-size: 1.1rem;
}

/* 权限布局 */
.permission-layout {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    margin-bottom: 2rem;
}

/* 面板通用样式 */
.role-selection-panel,
.permission-config-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: fit-content;
}

.panel-header {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    margin: 0;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.panel-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.panel-actions .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

.panel-actions .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.panel-actions .form-select {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.5rem;
    font-size: 0.85rem;
    min-width: 120px;
}

/* 角色选择面板 */
.role-selection-content {
    padding: 1.5rem;
}

.role-search {
    position: relative;
    margin-bottom: 1.5rem;
}

.role-search i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    z-index: 1;
}

.role-search input {
    padding-left: 2.5rem;
}

.role-list {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 2rem;
}

.role-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
    border: 2px solid transparent;
}

.role-item:hover {
    background: #f8f9fa;
    border-color: #e9ecef;
}

.role-item.selected {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-color: #4a90e2;
}

.role-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #4a90e2, #357abd);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.role-info .role-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.role-info .role-description {
    font-size: 0.8rem;
    color: #666;
    line-height: 1.3;
}

.role-info .role-users {
    font-size: 0.75rem;
    color: #4a90e2;
    margin-top: 0.25rem;
}

/* 角色统计 */
.role-stats {
    border-top: 1px solid #e9ecef;
    padding-top: 1.5rem;
}

.role-stats h4 {
    color: #2c3e50;
    font-size: 1rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.role-stats h4 i {
    color: #4a90e2;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
}

.stat-item {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    border: 1px solid #e9ecef;
}

.stat-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: #4a90e2;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.75rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 权限配置面板 */
.permission-content {
    padding: 2rem;
    min-height: 500px;
}

.welcome-message {
    text-align: center;
    color: #666;
    padding: 3rem 2rem;
}

.welcome-message i {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 1rem;
}

.welcome-message h4 {
    margin-bottom: 0.5rem;
    color: #999;
}

/* 权限树样式 */
.permission-tree {
    max-height: 600px;
    overflow-y: auto;
}

.permission-system {
    margin-bottom: 2rem;
}

.system-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.system-header:hover {
    background: linear-gradient(135deg, #e9ecef, #dee2e6);
}

.system-toggle {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.system-toggle i {
    font-size: 0.8rem;
    color: #666;
    transition: transform 0.3s ease;
}

.system-toggle.expanded i {
    transform: rotate(90deg);
}

.system-checkbox {
    width: 18px;
    height: 18px;
    accent-color: #4a90e2;
}

.system-icon {
    color: #4a90e2;
    font-size: 1.25rem;
}

.system-info .system-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1rem;
}

.system-info .system-count {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
}

.permission-functions {
    margin-left: 2rem;
    border-left: 2px solid #e9ecef;
    padding-left: 1.5rem;
    display: none;
}

.permission-functions.expanded {
    display: block;
}

.function-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.function-item:hover {
    background: #f8f9fa;
}

.function-item.checked {
    background: linear-gradient(135deg, #e8f5e8, #d4edda);
    border: 1px solid #c3e6cb;
}

.function-checkbox {
    width: 16px;
    height: 16px;
    accent-color: #28a745;
}

.function-icon {
    color: #28a745;
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

.function-info .function-name {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.function-info .function-code {
    font-size: 0.75rem;
    color: #666;
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 0.125rem 0.375rem;
    border-radius: 3px;
}

.function-actions {
    margin-left: auto;
    display: flex;
    gap: 0.25rem;
}

.function-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    min-width: auto;
}

/* 权限对比面板 */
.permission-compare-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    max-width: 1400px;
    margin: 0 auto;
    overflow: hidden;
}

.compare-content {
    padding: 2rem;
    min-height: 300px;
}

.compare-placeholder {
    text-align: center;
    color: #666;
    padding: 3rem 2rem;
}

.compare-placeholder i {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 1rem;
}

.compare-result {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.compare-role {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
}

.compare-role h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.compare-permissions {
    max-height: 400px;
    overflow-y: auto;
}

.compare-permission-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    border-radius: 4px;
    margin-bottom: 0.25rem;
}

.compare-permission-item.has-permission {
    background: #d4edda;
    color: #155724;
}

.compare-permission-item.no-permission {
    background: #f8d7da;
    color: #721c24;
}

.compare-permission-item.common-permission {
    background: #d1ecf1;
    color: #0c5460;
}

/* 批量操作样式 */
.batch-operation-content {
    max-height: 500px;
    overflow-y: auto;
}

.target-roles {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1rem;
}

.target-role-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.target-role-item:hover {
    background: #f8f9fa;
}

.target-role-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #4a90e2;
}

.permission-tree-mini {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .permission-layout {
        grid-template-columns: 300px 1fr;
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .permission-layout {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .permission-config-panel {
        order: 1;
    }
    
    .role-selection-panel {
        order: 2;
    }
    
    .panel-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .compare-result {
        grid-template-columns: 1fr;
    }
    
    .target-roles {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 1.5rem 1rem;
    }
    
    .page-header h2 {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* 动画效果 */
.function-item {
    animation: fadeInLeft 0.3s ease;
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.role-item {
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 滚动条样式 */
.role-list::-webkit-scrollbar,
.permission-tree::-webkit-scrollbar,
.compare-permissions::-webkit-scrollbar,
.batch-operation-content::-webkit-scrollbar {
    width: 6px;
}

.role-list::-webkit-scrollbar-track,
.permission-tree::-webkit-scrollbar-track,
.compare-permissions::-webkit-scrollbar-track,
.batch-operation-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.role-list::-webkit-scrollbar-thumb,
.permission-tree::-webkit-scrollbar-thumb,
.compare-permissions::-webkit-scrollbar-thumb,
.batch-operation-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.role-list::-webkit-scrollbar-thumb:hover,
.permission-tree::-webkit-scrollbar-thumb:hover,
.compare-permissions::-webkit-scrollbar-thumb:hover,
.batch-operation-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
