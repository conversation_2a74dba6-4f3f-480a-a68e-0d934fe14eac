/* 功能管理页面样式 */
.page-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.page-header h2 {
    color: #2c3e50;
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.page-header h2 i {
    color: #4a90e2;
}

.page-header p {
    color: #666;
    font-size: 1.1rem;
}

/* 内容布局 */
.content-layout {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    margin-bottom: 2rem;
}

/* 面板通用样式 */
.function-list-panel,
.function-config-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.panel-header {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    margin: 0;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.panel-actions {
    display: flex;
    gap: 0.5rem;
}

.panel-actions .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

.panel-actions .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

/* 搜索框 */
.search-box {
    position: relative;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.search-box i {
    position: absolute;
    left: 2rem;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    z-index: 1;
}

.search-box input {
    padding-left: 2.5rem;
}

/* 功能树样式 */
.function-tree {
    padding: 1rem;
    max-height: 600px;
    overflow-y: auto;
}

.tree-node {
    margin-bottom: 0.5rem;
}

.tree-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.tree-item:hover {
    background: #f8f9fa;
}

.tree-item.selected {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border: 1px solid #4a90e2;
}

.tree-item.disabled {
    opacity: 0.6;
}

.tree-item.disabled .item-name {
    color: #999;
    text-decoration: line-through;
}

.tree-toggle {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.tree-toggle:hover {
    background: #e9ecef;
}

.tree-toggle i {
    font-size: 0.8rem;
    color: #666;
    transition: transform 0.3s ease;
}

.tree-toggle.expanded i {
    transform: rotate(90deg);
}

.tree-checkbox {
    margin-right: 0.75rem;
    width: 16px;
    height: 16px;
    accent-color: #4a90e2;
}

.item-icon {
    margin-right: 0.75rem;
    color: #4a90e2;
    width: 16px;
    text-align: center;
}

.item-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.item-name {
    font-weight: 500;
    color: #2c3e50;
}

.item-code {
    font-size: 0.8rem;
    color: #666;
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    margin-left: 0.5rem;
}

.item-status {
    margin-left: 0.5rem;
}

.status-toggle {
    position: relative;
    width: 40px;
    height: 20px;
    background: #ccc;
    border-radius: 20px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.status-toggle.enabled {
    background: #28a745;
}

.status-toggle::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.status-toggle.enabled::after {
    transform: translateX(20px);
}

.tree-children {
    margin-left: 2rem;
    border-left: 2px solid #e9ecef;
    padding-left: 1rem;
    display: none;
}

.tree-children.expanded {
    display: block;
}

/* 功能配置面板 */
.config-content {
    padding: 2rem;
    min-height: 500px;
}

.welcome-message {
    text-align: center;
    color: #666;
    padding: 3rem 2rem;
}

.welcome-message i {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 1rem;
}

.welcome-message h4 {
    margin-bottom: 0.5rem;
    color: #999;
}

.config-form {
    max-width: 600px;
}

.config-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.config-header .function-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #4a90e2, #357abd);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.config-header .function-info h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.25rem;
}

.config-header .function-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

/* 批量操作面板 */
.batch-operations {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    max-width: 1400px;
    margin: 0 auto;
}

.batch-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.batch-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.batch-header h3 i {
    color: #4a90e2;
}

.selected-count {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.batch-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* 图标选择器 */
.icon-picker-content {
    max-width: 800px;
    max-height: 80vh;
}

.icon-search {
    margin-bottom: 1rem;
}

.icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 0.5rem;
    max-height: 400px;
    overflow-y: auto;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
}

.icon-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem 0.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.icon-item:hover {
    background: #f8f9fa;
    border-color: #4a90e2;
}

.icon-item.selected {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-color: #4a90e2;
}

.icon-item i {
    font-size: 1.5rem;
    color: #4a90e2;
    margin-bottom: 0.5rem;
}

.icon-item span {
    font-size: 0.7rem;
    color: #666;
    text-align: center;
    word-break: break-all;
}

/* 表单增强 */
.icon-selector {
    display: flex;
    gap: 0.5rem;
}

.icon-selector input {
    flex: 1;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-check-input {
    width: 18px;
    height: 18px;
    accent-color: #4a90e2;
}

.form-text {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
}

.required {
    color: #dc3545;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .content-layout {
        grid-template-columns: 350px 1fr;
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .content-layout {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .function-list-panel {
        order: 2;
    }
    
    .function-config-panel {
        order: 1;
    }
    
    .panel-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .batch-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .batch-actions {
        justify-content: center;
    }
    
    .tree-children {
        margin-left: 1rem;
        padding-left: 0.5rem;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 1.5rem 1rem;
    }
    
    .page-header h2 {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .icon-grid {
        grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
    }
    
    .batch-actions {
        flex-direction: column;
    }
    
    .batch-actions .btn {
        width: 100%;
    }
}

/* 动画效果 */
.tree-item {
    animation: fadeInLeft 0.3s ease;
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.config-content {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 滚动条样式 */
.function-tree::-webkit-scrollbar,
.icon-grid::-webkit-scrollbar {
    width: 6px;
}

.function-tree::-webkit-scrollbar-track,
.icon-grid::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.function-tree::-webkit-scrollbar-thumb,
.icon-grid::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.function-tree::-webkit-scrollbar-thumb:hover,
.icon-grid::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
