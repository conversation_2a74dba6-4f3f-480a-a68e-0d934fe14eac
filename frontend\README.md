# 金融软件项目管理系统 - 前端界面

## 项目概述

本项目是一个完整的金融软件项目管理系统前端界面，包含7个主要功能模块，专为金融软件项目管理实践课程设计。所有界面均为静态展示，包含丰富的模拟数据，适合截图和演示使用。

## 界面列表

### 1. 主页面 (index.html)
- **功能**: 系统导航中心，提供所有模块的入口
- **特色**: 现代化卡片式布局，系统状态监控
- **包含**: 7个功能模块的快速访问入口

### 2. 角色管理界面 (role-management.html)
- **功能**: 系统角色的增删改查管理
- **特色**: 完整的CRUD操作界面，支持搜索、分页、批量操作
- **包含**: 角色列表、新增/编辑弹窗、删除确认、导出功能

### 3. 功能管理界面 (function-management.html)
- **功能**: 系统功能模块的树形管理
- **特色**: 树形结构展示，图标选择器，批量启用/禁用
- **包含**: 功能树、图标库、配置面板、批量操作

### 4. 角色分配管理界面 (role-assignment.html)
- **功能**: 用户角色分配和管理
- **特色**: 双面板设计，用户列表与角色分配区域
- **包含**: 用户搜索、角色分配、批量操作、分配历史

### 5. 权限分配管理界面 (permission-assignment.html)
- **功能**: 角色权限配置和管理
- **特色**: 复杂的权限树结构，权限对比功能
- **包含**: 权限树、角色选择、权限对比、批量权限管理

### 6. 股票实时交易模拟界面 (stock-trading.html)
- **功能**: 股票实时行情查看和K线图分析
- **特色**: 实时数据表格，交互式图表，买五卖五盘口
- **包含**: 行情表格、K线图、盘口数据、成交明细、技术分析

### 7. 股票买卖操作界面 (stock-operations.html)
- **功能**: 股票买入卖出交易操作
- **特色**: 完整的交易流程，资金管理，交易验证
- **包含**: 股票搜索、买卖表单、资金计算、交易结果、持仓管理

### 8. 公司基本信息查询界面 (company-info.html)
- **功能**: 上市公司信息查询和财务分析
- **特色**: 多标签页设计，财务图表，PDF报告生成
- **包含**: 公司搜索、基本信息、财务数据、主营业务、报告下载

## 技术特点

### 前端技术栈
- **HTML5**: 语义化标签，现代化结构
- **CSS3**: Flexbox/Grid布局，动画效果，响应式设计
- **JavaScript ES6+**: 模块化编程，类组件，异步处理
- **Chart.js**: 财务图表和数据可视化
- **Font Awesome**: 图标库

### 设计特色
- **马卡龙配色**: 低饱和度的柔和色彩，视觉舒适
- **紧凑布局**: 优化间距和字体大小，减少滚动条，一页显示更多内容
- **响应式设计**: 支持桌面、平板、手机多端适配
- **现代化UI**: 卡片式布局，简洁背景，清爽效果
- **交互体验**: 平滑动画，悬浮效果，加载状态
- **数据可视化**: 图表展示，颜色编码，直观呈现

### 功能特性
- **模拟数据**: 丰富的模拟数据，真实的业务场景
- **表单验证**: 完整的输入验证和错误提示
- **搜索过滤**: 实时搜索，多条件筛选
- **批量操作**: 支持批量选择和操作
- **导出功能**: CSV导出，PDF报告生成

## 文件结构

```
frontend/
├── index.html                    # 主页面
├── role-management.html          # 角色管理
├── function-management.html      # 功能管理
├── role-assignment.html          # 角色分配管理
├── permission-assignment.html    # 权限分配管理
├── stock-trading.html            # 股票实时交易模拟
├── stock-operations.html         # 股票买卖操作
├── company-info.html             # 公司基本信息查询
├── css/
│   ├── common.css               # 通用样式
│   ├── macaron-theme.css        # 马卡龙主题样式（紧凑布局）
│   ├── index.css                # 主页样式
│   ├── role-management.css      # 角色管理样式
│   ├── function-management.css  # 功能管理样式
│   ├── role-assignment.css      # 角色分配样式
│   ├── permission-assignment.css # 权限分配样式
│   ├── stock-trading.css        # 股票交易样式
│   ├── stock-operations.css     # 股票操作样式
│   └── company-info.css         # 公司信息样式
└── js/
    ├── common.js                # 通用工具类
    ├── role-management.js       # 角色管理逻辑
    ├── function-management.js   # 功能管理逻辑
    ├── role-assignment.js       # 角色分配逻辑
    ├── permission-assignment.js # 权限分配逻辑
    ├── stock-trading.js         # 股票交易逻辑
    ├── stock-operations.js      # 股票操作逻辑
    └── company-info.js          # 公司信息逻辑
```

## 使用说明

### 本地运行
1. 将所有文件放在Web服务器目录下
2. 通过浏览器访问 `index.html`
3. 点击各个模块卡片进入对应界面

### 截图建议
- **主页面**: 展示系统整体架构和模块分布
- **管理界面**: 重点展示表格、表单、弹窗等交互元素
- **交易界面**: 突出实时数据、图表、交易流程
- **查询界面**: 强调搜索功能、多标签页、数据展示

### 演示要点
1. **界面美观**: 马卡龙配色设计，视觉舒适清爽
2. **布局紧凑**: 优化间距，减少滚动，一页显示更多内容
3. **功能完整**: 涵盖金融软件核心业务流程
4. **交互丰富**: 多种交互方式，用户体验好
5. **数据真实**: 模拟数据贴近实际业务场景
6. **响应式**: 支持多设备展示

## 注意事项

- 所有界面均为静态展示，不包含后端交互
- 数据为模拟数据，仅用于界面展示
- 建议使用现代浏览器（Chrome、Firefox、Safari、Edge）
- 部分功能需要JavaScript支持，请确保浏览器已启用JavaScript
- 图表功能依赖Chart.js库，需要网络连接加载CDN资源

## 适用场景

- 金融软件项目管理课程作业
- 前端界面设计参考
- 金融系统原型展示
- UI/UX设计案例
- 前端开发学习材料

---

**开发时间**: 2024年1月
**适用课程**: 金融软件项目管理实践
**界面数量**: 7个主要界面 + 1个导航页面
**技术栈**: HTML5 + CSS3 + JavaScript + Chart.js
