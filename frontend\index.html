<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>金融软件项目管理系统</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/index.css">
    <link rel="stylesheet" href="css/macaron-theme.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                <h1>金融软件项目管理系统</h1>
            </div>
            <nav class="nav">
                <div class="user-info">
                    <span>欢迎，管理员</span>
                    <i class="fas fa-user-circle"></i>
                </div>
            </nav>
        </header>

        <main class="main">
            <div class="dashboard">
                <h2>系统功能导航</h2>
                <div class="module-grid">
                    <!-- 权限管理模块 -->
                    <div class="module-section">
                        <h3><i class="fas fa-shield-alt"></i> 权限管理模块</h3>
                        <div class="module-cards">
                            <div class="module-card" onclick="navigateTo('role-management.html')">
                                <div class="card-icon">
                                    <i class="fas fa-users-cog"></i>
                                </div>
                                <div class="card-content">
                                    <h4>角色管理</h4>
                                    <p>管理系统角色，配置角色权限</p>
                                </div>
                            </div>
                            <div class="module-card" onclick="navigateTo('function-management.html')">
                                <div class="card-icon">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <div class="card-content">
                                    <h4>功能管理</h4>
                                    <p>管理系统功能模块和权限配置</p>
                                </div>
                            </div>
                            <div class="module-card" onclick="navigateTo('role-assignment.html')">
                                <div class="card-icon">
                                    <i class="fas fa-user-tag"></i>
                                </div>
                                <div class="card-content">
                                    <h4>角色分配管理</h4>
                                    <p>为用户分配相应的系统角色</p>
                                </div>
                            </div>
                            <div class="module-card" onclick="navigateTo('permission-assignment.html')">
                                <div class="card-icon">
                                    <i class="fas fa-key"></i>
                                </div>
                                <div class="card-content">
                                    <h4>权限分配管理</h4>
                                    <p>配置角色的具体功能权限</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 业务功能模块 -->
                    <div class="module-section">
                        <h3><i class="fas fa-chart-bar"></i> 业务功能模块</h3>
                        <div class="module-cards">
                            <div class="module-card" onclick="navigateTo('stock-trading.html')">
                                <div class="card-icon">
                                    <i class="fas fa-chart-candlestick"></i>
                                </div>
                                <div class="card-content">
                                    <h4>股票实时交易模拟</h4>
                                    <p>实时股票行情查看和模拟交易</p>
                                </div>
                            </div>
                            <div class="module-card" onclick="navigateTo('stock-operations.html')">
                                <div class="card-icon">
                                    <i class="fas fa-exchange-alt"></i>
                                </div>
                                <div class="card-content">
                                    <h4>股票买卖操作</h4>
                                    <p>执行股票买入卖出交易操作</p>
                                </div>
                            </div>
                            <div class="module-card" onclick="navigateTo('company-info.html')">
                                <div class="card-icon">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div class="card-content">
                                    <h4>公司基本信息查询</h4>
                                    <p>查询上市公司详细信息和财务数据</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统状态 -->
                <div class="system-status">
                    <h3><i class="fas fa-info-circle"></i> 系统状态</h3>
                    <div class="status-grid">
                        <div class="status-item">
                            <div class="status-icon online">
                                <i class="fas fa-circle"></i>
                            </div>
                            <div class="status-info">
                                <span class="status-label">系统状态</span>
                                <span class="status-value">正常运行</span>
                            </div>
                        </div>
                        <div class="status-item">
                            <div class="status-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="status-info">
                                <span class="status-label">在线用户</span>
                                <span class="status-value">156</span>
                            </div>
                        </div>
                        <div class="status-item">
                            <div class="status-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="status-info">
                                <span class="status-label">数据更新</span>
                                <span class="status-value">实时</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="js/common.js"></script>
    <script>
        function navigateTo(page) {
            window.location.href = page;
        }

        // 添加一些动态效果
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.module-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('fade-in');
            });
        });
    </script>
</body>
</html>
