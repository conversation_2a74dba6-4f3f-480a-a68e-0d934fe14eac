/* 通用样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
    color: #6c757d;
    font-size: 12px;
    line-height: 1.4;
}

.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(8px);
    padding: 0.75rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.06);
    border-bottom: 1px solid rgba(220, 220, 220, 0.3);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo i {
    font-size: 1.5rem;
    color: #c8a2c8;
}

.logo h1 {
    color: #8e8e93;
    font-size: 1.2rem;
    font-weight: 500;
}

.nav .user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #a8a8a8;
    font-size: 11px;
}

.nav .user-info i {
    font-size: 1.2rem;
    color: #c8a2c8;
}

/* 主要内容区域 */
.main {
    flex: 1;
    padding: 1rem;
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 11px;
    font-weight: 400;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    text-decoration: none;
}

.btn-primary {
    background: #b8c6db;
    color: white;
}

.btn-primary:hover {
    background: #a8b5d1;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(184, 198, 219, 0.3);
}

.btn-success {
    background: #c8d6e5;
    color: white;
}

.btn-success:hover {
    background: #b8c6db;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(200, 214, 229, 0.3);
}

.btn-danger {
    background: #f8a5c2;
    color: white;
}

.btn-danger:hover {
    background: #f78fb3;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(248, 165, 194, 0.3);
}

.btn-secondary {
    background: #d1d8e0;
    color: #8e8e93;
    border: 1px solid #c8d6e5;
}

.btn-secondary:hover {
    background: #c8d6e5;
    border-color: #b8c6db;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(209, 216, 224, 0.3);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* 表格样式 */
.table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.table {
    width: 100%;
    border-collapse: collapse;
    font-size: 11px;
}

.table th,
.table td {
    padding: 0.6rem 0.8rem;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.table th {
    background: #f8f9fa;
    font-weight: 500;
    color: #8e8e93;
}

.table tbody tr:hover {
    background: #fafbfc;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.4rem;
    font-weight: 400;
    color: #8e8e93;
    font-size: 11px;
}

.form-control {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    font-size: 11px;
    transition: all 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #c8a2c8;
    box-shadow: 0 0 0 2px rgba(200, 162, 200, 0.1);
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(3px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    max-width: 450px;
    width: 90%;
    max-height: 85vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    animation: modalSlideIn 0.2s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #f0f0f0;
}

.modal-title {
    font-size: 1rem;
    font-weight: 500;
    color: #8e8e93;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #c8c8c8;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f8f9fa;
    color: #a8a8a8;
}

.modal-footer {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
    padding-top: 0.75rem;
    border-top: 1px solid #f0f0f0;
}

/* 提示消息样式 */
.alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border: 1px solid transparent;
}

.alert-success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-warning {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-info {
    background: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #4a90e2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header {
        padding: 1rem;
    }
    
    .main {
        padding: 1rem;
    }
    
    .modal-content {
        margin: 1rem;
        width: calc(100% - 2rem);
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.6s ease forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 状态指示器 */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}
