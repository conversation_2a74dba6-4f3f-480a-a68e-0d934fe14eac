/* 马卡龙主题覆盖样式 - 紧凑布局 */

/* 全局字体和间距调整 */
body {
    font-size: 11px !important;
    line-height: 1.3 !important;
}

/* 页面头部统一样式 */
.page-header {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(6px) !important;
    border-radius: 8px !important;
    padding: 1rem 1.5rem !important;
    margin-bottom: 1rem !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

.page-header h2 {
    color: #8e8e93 !important;
    font-size: 1.3rem !important;
    font-weight: 500 !important;
    margin-bottom: 0.3rem !important;
}

.page-header p {
    color: #a8a8a8 !important;
    font-size: 11px !important;
}

/* 内容区域紧凑化 */
.main {
    padding: 0.75rem !important;
}

.content-layout {
    gap: 1rem !important;
}

/* 表格紧凑化 */
.table {
    font-size: 10px !important;
}

.table th,
.table td {
    padding: 0.5rem 0.6rem !important;
}

.table th {
    background: #f8f9fa !important;
    color: #8e8e93 !important;
    font-weight: 500 !important;
    font-size: 10px !important;
}

/* 按钮紧凑化 */
.btn {
    padding: 0.4rem 0.8rem !important;
    font-size: 10px !important;
    border-radius: 5px !important;
}

/* 表单控件紧凑化 */
.form-control {
    padding: 0.4rem 0.6rem !important;
    font-size: 10px !important;
    border-radius: 5px !important;
}

.form-label {
    font-size: 10px !important;
    margin-bottom: 0.3rem !important;
    color: #8e8e93 !important;
}

.form-group {
    margin-bottom: 0.8rem !important;
}

/* 模态框紧凑化 */
.modal-content {
    padding: 1.2rem !important;
    border-radius: 6px !important;
    max-width: 400px !important;
}

.modal-title {
    font-size: 1rem !important;
    color: #8e8e93 !important;
}

.modal-header {
    margin-bottom: 0.8rem !important;
    padding-bottom: 0.6rem !important;
}

.modal-footer {
    margin-top: 1.2rem !important;
    padding-top: 0.6rem !important;
    gap: 0.6rem !important;
}

/* 搜索区域紧凑化 */
.search-section {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(6px) !important;
    border-radius: 8px !important;
    padding: 1rem !important;
    margin-bottom: 1rem !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

.search-form {
    gap: 0.6rem !important;
}

.search-group label {
    font-size: 10px !important;
    color: #8e8e93 !important;
    margin-bottom: 0.3rem !important;
}

.search-group input,
.search-group select {
    padding: 0.4rem 0.6rem !important;
    font-size: 10px !important;
    border-radius: 5px !important;
}

/* 卡片和面板紧凑化 */
.card,
.panel {
    border-radius: 6px !important;
    padding: 1rem !important;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.04) !important;
}

.card-header {
    padding: 0.8rem 1rem !important;
    font-size: 11px !important;
    font-weight: 500 !important;
    color: #8e8e93 !important;
}

/* 侧边栏紧凑化 */
.sidebar {
    padding: 1rem !important;
}

.sidebar-section {
    margin-bottom: 1.5rem !important;
}

.sidebar-section h3 {
    font-size: 1rem !important;
    color: #8e8e93 !important;
    margin-bottom: 0.8rem !important;
}

/* 操作按钮组紧凑化 */
.action-buttons {
    gap: 0.5rem !important;
}

.toolbar {
    padding: 0.8rem !important;
    gap: 0.6rem !important;
}

/* 分页器紧凑化 */
.pagination {
    gap: 0.3rem !important;
}

.pagination .btn {
    padding: 0.3rem 0.6rem !important;
    font-size: 10px !important;
}

/* 状态指示器紧凑化 */
.status-badge {
    padding: 0.2rem 0.5rem !important;
    font-size: 9px !important;
    border-radius: 3px !important;
}

/* 图表容器紧凑化 */
.chart-container {
    height: 250px !important;
    margin-bottom: 1rem !important;
}

/* 标签页紧凑化 */
.nav-tabs {
    margin-bottom: 1rem !important;
}

.nav-tabs .nav-link {
    padding: 0.5rem 1rem !important;
    font-size: 11px !important;
}

.tab-content {
    padding: 1rem !important;
}

/* 列表项紧凑化 */
.list-group-item {
    padding: 0.6rem 1rem !important;
    font-size: 11px !important;
}

/* 输入组紧凑化 */
.input-group {
    margin-bottom: 0.8rem !important;
}

.input-group-text {
    padding: 0.4rem 0.6rem !important;
    font-size: 10px !important;
}

/* 警告和提示紧凑化 */
.alert {
    padding: 0.6rem 1rem !important;
    font-size: 11px !important;
    border-radius: 5px !important;
    margin-bottom: 1rem !important;
}

/* 进度条紧凑化 */
.progress {
    height: 0.5rem !important;
    border-radius: 3px !important;
}

/* 面包屑紧凑化 */
.breadcrumb {
    padding: 0.5rem 1rem !important;
    font-size: 10px !important;
    margin-bottom: 1rem !important;
}

/* 下拉菜单紧凑化 */
.dropdown-menu {
    font-size: 10px !important;
    border-radius: 5px !important;
}

.dropdown-item {
    padding: 0.4rem 1rem !important;
}

/* 工具提示紧凑化 */
.tooltip {
    font-size: 10px !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .main {
        padding: 0.5rem !important;
    }
    
    .page-header {
        padding: 0.8rem 1rem !important;
    }
    
    .page-header h2 {
        font-size: 1.1rem !important;
    }
    
    .content-layout {
        grid-template-columns: 1fr !important;
        gap: 0.8rem !important;
    }
    
    .modal-content {
        max-width: 95% !important;
        padding: 1rem !important;
    }
}

/* 特定页面的图标颜色 */
.page-header h2 i {
    font-size: 1.2rem !important;
}

/* 角色管理页面 */
body[data-page="role-management"] .page-header h2 i { color: #f8c291 !important; }
/* 功能管理页面 */
body[data-page="function-management"] .page-header h2 i { color: #c8d6e5 !important; }
/* 角色分配页面 */
body[data-page="role-assignment"] .page-header h2 i { color: #f8a5c2 !important; }
/* 权限分配页面 */
body[data-page="permission-assignment"] .page-header h2 i { color: #c8a2c8 !important; }
/* 股票交易页面 */
body[data-page="stock-trading"] .page-header h2 i { color: #b8c6db !important; }
/* 股票操作页面 */
body[data-page="stock-operations"] .page-header h2 i { color: #d1d8e0 !important; }
/* 公司信息页面 */
body[data-page="company-info"] .page-header h2 i { color: #f6b93b !important; }

/* 股票交易页面特殊布局优化 */
body[data-page="stock-trading"] .trading-container {
    height: calc(100vh - 140px) !important;
    grid-template-rows: auto 1fr !important;
    gap: 0.8rem !important;
}

body[data-page="stock-trading"] .market-data-grid {
    grid-template-columns: 2fr 1fr !important;
    gap: 0.8rem !important;
    height: 100% !important;
}

body[data-page="stock-trading"] .chart-container {
    height: 200px !important;
}

body[data-page="stock-trading"] .market-table {
    max-height: 300px !important;
    overflow-y: auto !important;
}

body[data-page="stock-trading"] .order-book {
    max-height: 250px !important;
    overflow-y: auto !important;
}

/* 股票操作页面布局优化 */
body[data-page="stock-operations"] .operations-container {
    height: calc(100vh - 140px) !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 1rem !important;
}

body[data-page="stock-operations"] .stock-info-panel {
    max-height: 400px !important;
    overflow-y: auto !important;
}

/* 公司信息页面布局优化 */
body[data-page="company-info"] .company-container {
    height: calc(100vh - 140px) !important;
}

body[data-page="company-info"] .tab-content {
    max-height: 400px !important;
    overflow-y: auto !important;
}

body[data-page="company-info"] .chart-container {
    height: 180px !important;
}
