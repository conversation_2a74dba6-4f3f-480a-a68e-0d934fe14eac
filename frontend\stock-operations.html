<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票买卖操作 - 金融软件项目管理系统</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/stock-operations.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                <h1>金融软件项目管理系统</h1>
            </div>
            <nav class="nav">
                <a href="index.html" class="btn btn-secondary">
                    <i class="fas fa-home"></i> 返回首页
                </a>
                <a href="stock-trading.html" class="btn btn-secondary">
                    <i class="fas fa-chart-candlestick"></i> 实时行情
                </a>
                <div class="user-info">
                    <span>欢迎，交易员</span>
                    <i class="fas fa-user-circle"></i>
                </div>
            </nav>
        </header>

        <main class="main">
            <div class="page-header">
                <h2><i class="fas fa-exchange-alt"></i> 股票买卖操作</h2>
                <div class="account-summary">
                    <div class="account-item">
                        <span class="account-label">总资产</span>
                        <span class="account-value">¥1,000,000.00</span>
                    </div>
                    <div class="account-item">
                        <span class="account-label">可用资金</span>
                        <span class="account-value available-funds">¥500,000.00</span>
                    </div>
                    <div class="account-item">
                        <span class="account-label">持仓市值</span>
                        <span class="account-value">¥500,000.00</span>
                    </div>
                    <div class="account-item">
                        <span class="account-label">今日盈亏</span>
                        <span class="account-value profit">+¥12,500.00</span>
                    </div>
                </div>
            </div>

            <div class="operations-layout">
                <!-- 左侧股票信息 -->
                <div class="stock-info-panel">
                    <div class="panel-header">
                        <h3><i class="fas fa-info-circle"></i> 股票信息</h3>
                        <div class="panel-actions">
                            <button class="btn btn-secondary" onclick="refreshStockInfo()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                    
                    <div class="stock-search-section">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="stockSearchInput" placeholder="输入股票代码或名称..." class="form-control">
                            <button class="btn btn-primary" onclick="searchStockInfo()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <div class="search-suggestions" id="stockSuggestions">
                            <!-- 搜索建议将动态显示 -->
                        </div>
                    </div>

                    <div class="current-stock-info" id="currentStockInfo">
                        <div class="no-stock-selected">
                            <i class="fas fa-chart-line"></i>
                            <h4>请选择股票</h4>
                            <p>请在上方搜索框中输入股票代码或名称来查看股票信息</p>
                        </div>
                    </div>

                    <div class="quick-stock-buttons">
                        <h4><i class="fas fa-star"></i> 热门股票</h4>
                        <div class="quick-buttons-grid">
                            <button class="quick-stock-btn" onclick="selectStockForTrading('000001')">
                                <div class="stock-code">000001</div>
                                <div class="stock-name">平安银行</div>
                                <div class="stock-price price-up">12.85</div>
                            </button>
                            <button class="quick-stock-btn" onclick="selectStockForTrading('600036')">
                                <div class="stock-code">600036</div>
                                <div class="stock-name">招商银行</div>
                                <div class="stock-price price-up">35.68</div>
                            </button>
                            <button class="quick-stock-btn" onclick="selectStockForTrading('600519')">
                                <div class="stock-code">600519</div>
                                <div class="stock-name">贵州茅台</div>
                                <div class="stock-price price-up">1685.50</div>
                            </button>
                            <button class="quick-stock-btn" onclick="selectStockForTrading('000858')">
                                <div class="stock-code">000858</div>
                                <div class="stock-name">五粮液</div>
                                <div class="stock-price price-up">128.45</div>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 右侧交易表单 -->
                <div class="trading-form-panel">
                    <div class="panel-header">
                        <div class="trade-type-switcher">
                            <button class="trade-type-btn buy-btn active" onclick="switchTradeType('buy')">
                                <i class="fas fa-arrow-up"></i> 买入
                            </button>
                            <button class="trade-type-btn sell-btn" onclick="switchTradeType('sell')">
                                <i class="fas fa-arrow-down"></i> 卖出
                            </button>
                        </div>
                        <div class="panel-actions">
                            <button class="btn btn-secondary" onclick="resetForm()">
                                <i class="fas fa-undo"></i> 重置
                            </button>
                        </div>
                    </div>

                    <div class="trading-form-content">
                        <!-- 买入表单 -->
                        <div class="trade-form buy-form active" id="buyForm">
                            <form id="buyOrderForm">
                                <div class="form-group">
                                    <label class="form-label">股票代码 <span class="required">*</span></label>
                                    <div class="stock-input-group">
                                        <input type="text" class="form-control" id="buyStockCode" name="stockCode" placeholder="请输入股票代码" required>
                                        <button type="button" class="btn btn-secondary" onclick="openStockSelector('buy')">
                                            <i class="fas fa-list"></i> 选择
                                        </button>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">买入数量 <span class="required">*</span></label>
                                    <div class="quantity-input-group">
                                        <input type="number" class="form-control" id="buyQuantity" name="quantity" 
                                               placeholder="请输入买入数量" min="100" step="100" required>
                                        <span class="input-hint">需为100的整数倍</span>
                                    </div>
                                    <div class="quantity-buttons">
                                        <button type="button" class="btn btn-secondary" onclick="setQuantity('buy', 1000)">1000股</button>
                                        <button type="button" class="btn btn-secondary" onclick="setQuantity('buy', 2000)">2000股</button>
                                        <button type="button" class="btn btn-secondary" onclick="setQuantity('buy', 5000)">5000股</button>
                                        <button type="button" class="btn btn-secondary" onclick="calculateMaxBuyQuantity()">最大可买</button>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">价格类型</label>
                                    <div class="price-type-group">
                                        <div class="radio-group">
                                            <label class="radio-item">
                                                <input type="radio" name="buyPriceType" value="market" checked onchange="handlePriceTypeChange('buy')">
                                                <span class="radio-label">实时现价</span>
                                            </label>
                                            <label class="radio-item">
                                                <input type="radio" name="buyPriceType" value="limit" onchange="handlePriceTypeChange('buy')">
                                                <span class="radio-label">限价</span>
                                            </label>
                                        </div>
                                        <div class="limit-price-input" id="buyLimitPriceInput" style="display: none;">
                                            <input type="number" class="form-control" id="buyLimitPrice" name="limitPrice" 
                                                   placeholder="请输入限价" step="0.01" min="0">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="funds-info">
                                        <div class="funds-item">
                                            <span class="funds-label">可用资金：</span>
                                            <span class="funds-value" id="availableFunds">¥500,000.00</span>
                                        </div>
                                        <div class="funds-item">
                                            <span class="funds-label">预计费用：</span>
                                            <span class="funds-value" id="estimatedCost">¥0.00</span>
                                        </div>
                                        <div class="funds-item">
                                            <span class="funds-label">剩余资金：</span>
                                            <span class="funds-value" id="remainingFunds">¥500,000.00</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <button type="submit" class="btn btn-success btn-large" id="buySubmitBtn" disabled>
                                        <i class="fas fa-arrow-up"></i> 确认买入
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- 卖出表单 -->
                        <div class="trade-form sell-form" id="sellForm">
                            <form id="sellOrderForm">
                                <div class="form-group">
                                    <label class="form-label">股票代码 <span class="required">*</span></label>
                                    <select class="form-control form-select" id="sellStockCode" name="stockCode" required>
                                        <option value="">请选择持仓股票</option>
                                        <!-- 持仓股票选项将动态加载 -->
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">卖出数量 <span class="required">*</span></label>
                                    <div class="quantity-input-group">
                                        <input type="number" class="form-control" id="sellQuantity" name="quantity" 
                                               placeholder="请输入卖出数量" min="100" step="100" required>
                                        <span class="input-hint" id="maxSellHint">最大可卖：0 股</span>
                                    </div>
                                    <div class="quantity-buttons">
                                        <button type="button" class="btn btn-secondary" onclick="setSellQuantity(0.25)">1/4仓位</button>
                                        <button type="button" class="btn btn-secondary" onclick="setSellQuantity(0.5)">1/2仓位</button>
                                        <button type="button" class="btn btn-secondary" onclick="setSellQuantity(0.75)">3/4仓位</button>
                                        <button type="button" class="btn btn-secondary" onclick="setSellQuantity(1)">全部卖出</button>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">价格类型</label>
                                    <div class="price-type-group">
                                        <div class="radio-group">
                                            <label class="radio-item">
                                                <input type="radio" name="sellPriceType" value="market" checked onchange="handlePriceTypeChange('sell')">
                                                <span class="radio-label">实时现价</span>
                                            </label>
                                            <label class="radio-item">
                                                <input type="radio" name="sellPriceType" value="limit" onchange="handlePriceTypeChange('sell')">
                                                <span class="radio-label">限价</span>
                                            </label>
                                        </div>
                                        <div class="limit-price-input" id="sellLimitPriceInput" style="display: none;">
                                            <input type="number" class="form-control" id="sellLimitPrice" name="limitPrice" 
                                                   placeholder="请输入限价" step="0.01" min="0">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="holding-info" id="holdingInfo">
                                        <div class="holding-item">
                                            <span class="holding-label">持仓数量：</span>
                                            <span class="holding-value" id="holdingQuantity">0 股</span>
                                        </div>
                                        <div class="holding-item">
                                            <span class="holding-label">成本价格：</span>
                                            <span class="holding-value" id="holdingCostPrice">¥0.00</span>
                                        </div>
                                        <div class="holding-item">
                                            <span class="holding-label">预计收入：</span>
                                            <span class="holding-value" id="estimatedIncome">¥0.00</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <button type="submit" class="btn btn-danger btn-large" id="sellSubmitBtn" disabled>
                                        <i class="fas fa-arrow-down"></i> 确认卖出
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 交易结果区域 -->
            <div class="trading-result-panel">
                <div class="panel-header">
                    <h3><i class="fas fa-receipt"></i> 交易结果</h3>
                    <div class="panel-actions">
                        <button class="btn btn-secondary" onclick="clearResults()">
                            <i class="fas fa-trash"></i> 清空记录
                        </button>
                        <button class="btn btn-secondary" onclick="exportResults()">
                            <i class="fas fa-download"></i> 导出记录
                        </button>
                    </div>
                </div>
                <div class="trading-results" id="tradingResults">
                    <div class="no-results">
                        <i class="fas fa-clipboard-list"></i>
                        <p>暂无交易记录</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 股票选择器模态框 -->
    <div class="modal" id="stockSelectorModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">选择股票</h3>
                <button class="modal-close" onclick="closeStockSelector()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="stock-selector-search">
                    <input type="text" class="form-control" id="selectorSearch" placeholder="搜索股票代码或名称...">
                </div>
                <div class="stock-selector-list" id="stockSelectorList">
                    <!-- 股票列表将动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeStockSelector()">取消</button>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script src="js/stock-operations.js"></script>
</body>
</html>
