/* 首页专用样式 */
.dashboard {
    max-width: 1400px;
    margin: 0 auto;
}

.dashboard h2 {
    color: #8e8e93;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    text-align: center;
    text-shadow: none;
    font-weight: 500;
}

.module-grid {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.module-section {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(6px);
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(240, 240, 240, 0.5);
}

.module-section h3 {
    color: #8e8e93;
    font-size: 1.2rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.module-section h3 i {
    color: #c8a2c8;
    font-size: 1.3rem;
}

.module-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1rem;
}

.module-card {
    background: #ffffff;
    border-radius: 8px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.04);
}

.module-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-color: #e0e0e0;
    background: #fafbfc;
}

.card-icon {
    width: 45px;
    height: 45px;
    background: #f8c291;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.card-content h4 {
    color: #8e8e93;
    font-size: 13px;
    margin-bottom: 0.3rem;
    font-weight: 500;
}

.card-content p {
    color: #a8a8a8;
    font-size: 11px;
    line-height: 1.3;
}

/* 系统状态区域 */
.system-status {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(6px);
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1.5rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(240, 240, 240, 0.5);
}

.system-status h3 {
    color: #8e8e93;
    font-size: 1.2rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.system-status h3 i {
    color: #c8a2c8;
    font-size: 1.3rem;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
}

.status-icon {
    width: 35px;
    height: 35px;
    background: #d1d8e0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.status-icon.online {
    background: #c8d6e5;
}

.status-info {
    display: flex;
    flex-direction: column;
}

.status-label {
    font-size: 10px;
    color: #a8a8a8;
    margin-bottom: 0.2rem;
}

.status-value {
    font-size: 12px;
    font-weight: 500;
    color: #8e8e93;
}

/* 不同模块的马卡龙配色 */
.module-card:nth-child(1) .card-icon { background: #f8c291; } /* 角色管理 - 桃色 */
.module-card:nth-child(2) .card-icon { background: #c8d6e5; } /* 功能管理 - 蓝色 */
.module-card:nth-child(3) .card-icon { background: #f8a5c2; } /* 角色分配 - 粉色 */
.module-card:nth-child(4) .card-icon { background: #c8a2c8; } /* 权限分配 - 紫色 */
.module-card:nth-child(5) .card-icon { background: #b8c6db; } /* 股票交易 - 灰蓝 */
.module-card:nth-child(6) .card-icon { background: #d1d8e0; } /* 股票操作 - 灰色 */
.module-card:nth-child(7) .card-icon { background: #f6b93b; } /* 公司信息 - 黄色 */

/* 特殊图标样式 */
.fa-chart-candlestick::before {
    content: "\f0e7"; /* 使用现有的图标作为替代 */
}

/* 响应式设计 */
@media (max-width: 768px) {
    .dashboard h2 {
        font-size: 1.5rem;
    }
    
    .module-section {
        padding: 1.5rem;
    }
    
    .module-cards {
        grid-template-columns: 1fr;
    }
    
    .module-card {
        flex-direction: column;
        text-align: center;
        padding: 2rem 1.5rem;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .dashboard {
        padding: 0 1rem;
    }
    
    .module-section {
        padding: 1rem;
    }
    
    .module-card {
        padding: 1.5rem 1rem;
    }
}

/* 动画增强 */
.module-card {
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 悬浮效果增强 */
.module-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(74, 144, 226, 0.1), rgba(53, 122, 189, 0.1));
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.module-card {
    position: relative;
    overflow: hidden;
}

.module-card:hover::before {
    opacity: 1;
}

/* 加载状态 */
.module-card.loading {
    pointer-events: none;
    opacity: 0.7;
}

.module-card.loading .card-icon {
    background: #ccc;
}

.module-card.loading .card-content {
    color: #999;
}

/* 成功状态指示 */
.status-item.success .status-icon {
    background: linear-gradient(135deg, #28a745, #20c997);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}
