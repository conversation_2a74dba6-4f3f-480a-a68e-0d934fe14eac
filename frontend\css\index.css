/* 首页专用样式 */
.dashboard {
    max-width: 1200px;
    margin: 0 auto;
}

.dashboard h2 {
    color: white;
    font-size: 2rem;
    margin-bottom: 2rem;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.module-grid {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.module-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.module-section h3 {
    color: #2c3e50;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.module-section h3 i {
    color: #4a90e2;
    font-size: 1.75rem;
}

.module-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.module-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.module-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    border-color: #4a90e2;
    background: linear-gradient(135deg, #ffffff, #f0f7ff);
}

.card-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #4a90e2, #357abd);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.card-content h4 {
    color: #2c3e50;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.card-content p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* 系统状态区域 */
.system-status {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 2rem;
    margin-top: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.system-status h3 {
    color: #2c3e50;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.system-status h3 i {
    color: #4a90e2;
    font-size: 1.75rem;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.status-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #6c757d, #495057);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.status-icon.online {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.status-info {
    display: flex;
    flex-direction: column;
}

.status-label {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.status-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

/* 特殊图标样式 */
.fa-chart-candlestick::before {
    content: "\f0e7"; /* 使用现有的图标作为替代 */
}

/* 响应式设计 */
@media (max-width: 768px) {
    .dashboard h2 {
        font-size: 1.5rem;
    }
    
    .module-section {
        padding: 1.5rem;
    }
    
    .module-cards {
        grid-template-columns: 1fr;
    }
    
    .module-card {
        flex-direction: column;
        text-align: center;
        padding: 2rem 1.5rem;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .dashboard {
        padding: 0 1rem;
    }
    
    .module-section {
        padding: 1rem;
    }
    
    .module-card {
        padding: 1.5rem 1rem;
    }
}

/* 动画增强 */
.module-card {
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 悬浮效果增强 */
.module-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(74, 144, 226, 0.1), rgba(53, 122, 189, 0.1));
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.module-card {
    position: relative;
    overflow: hidden;
}

.module-card:hover::before {
    opacity: 1;
}

/* 加载状态 */
.module-card.loading {
    pointer-events: none;
    opacity: 0.7;
}

.module-card.loading .card-icon {
    background: #ccc;
}

.module-card.loading .card-content {
    color: #999;
}

/* 成功状态指示 */
.status-item.success .status-icon {
    background: linear-gradient(135deg, #28a745, #20c997);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}
